package com.edu.account.model.dto.school;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算机构批量设置返费比例DTO
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(value = "FinanceSettlementOrgBatchRefundRateDTO", description = "结算机构批量设置返费比例DTO")
public class FinanceSettlementOrgBatchRefundRateDTO {

    @ApiModelProperty(value = "返费比例(0-1之间的小数，支持4位小数)", required = true)
    @NotNull(message = "返费比例不能为空")
    @DecimalMin(value = "0.0000", message = "返费比例不能小于0")
    @DecimalMax(value = "1.0000", message = "返费比例不能大于1")
    private BigDecimal refundRate;

    @ApiModelProperty(value = "机构ID列表", required = true)
    @NotEmpty(message = "机构ID列表不能为空")
    private List<Long> ids;
}
