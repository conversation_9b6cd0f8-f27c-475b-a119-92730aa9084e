package com.edu.account.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreVO {
    @ApiModelProperty(value = "学员信息", name = "studentInfo")
    private StudentInfo studentInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StudentInfo {
        @ApiModelProperty(value = "姓名", name = "studentName")
        private String studentName;

        @ApiModelProperty(value = "性别", name = "sex")
        private String sex;

        @ApiModelProperty(value = "民族", name = "nation")
        private String nation;

        @ApiModelProperty(value = "身份证号", name = "idNumber")
        private String idNumber;

        @ApiModelProperty(value = "专业", name = "specialtyName")
        private String specialtyName;

        @ApiModelProperty(value = "学号", name = "studentNumber")
        private String studentNumber;

        @ApiModelProperty(value = "教学单位", name = "orgName")
        private String oneOrgName;

        @ApiModelProperty(value = "学制", name = "schoolSys")
        private BigDecimal schoolSys;

        @ApiModelProperty(value = "层次", name = "levelName")
        private String levelName;

        @ApiModelProperty(value = "年级", name = "gradeName")
        private String gradeName;

        @ApiModelProperty(value = "一学年", name = "schoolOneYear")
        private String schoolOneYear;
        @ApiModelProperty(value = "二学年", name = "schoolTwoYear")
        private String schoolTwoYear;
        @ApiModelProperty(value = "三学年", name = "schoolThreeYear")
        private String schoolThreeYear;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreList {
        /**
         * 序号
         */
        private Integer indexNum;

        /**
         * 课程名称
         */
        private String courseName1;

        /**
         * 成绩
         */
        private BigDecimal score1;
        /**
         * 课程名称
         */
        private String courseName2;

        /**
         * 成绩
         */
        private BigDecimal score2;

        /**
         * 课程名称
         */
        private String courseName3;

        /**
         * 成绩
         */
        private BigDecimal score3;
        /**
         * 课程名称
         */
        private String courseName4;

        /**
         * 成绩
         */
        private BigDecimal score4;

        /**
         * 课程名称
         */
        private String courseName5;

        /**
         * 成绩
         */
        private BigDecimal score5;
    }
}