package com.edu.account.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算学员明细管理VO
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(value = "FinanceSettlementStudentVo", description = "结算学员明细管理VO")
public class FinanceSettlementStudentVo {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "结算批次ID")
    private Long settlementBatchId;

    @ApiModelProperty(value = "结算业务ID")
    private Long settlementBusinessId;

    @ApiModelProperty(value = "所属机构名称")
    private String orgName;

    @ApiModelProperty(value = "录取大表ID")
    private Long enrollId;

    @ApiModelProperty(value = "学员ID")
    private Long studentId;

    @ApiModelProperty(value = "学员姓名")
    private String studentName;

    @ApiModelProperty(value = "身份证号")
    private String idNum;

    @ApiModelProperty(value = "学校名称")
    private String schoolName;

    @ApiModelProperty(value = "专业")
    private String specialty;

    @ApiModelProperty(value = "层次")
    private String level;

    @ApiModelProperty(value = "年级/学员批次")
    private String grade;

    @ApiModelProperty(value = "缴费年度")
    private String paymentYear;

    @ApiModelProperty(value = "缴费学年")
    private String paymentAcademicYear;

    @ApiModelProperty(value = "应缴金额")
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "实缴金额")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "返费金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "缴费订单ID")
    private Long paymentOrderId;

    @ApiModelProperty(value = "缴费时间")
    private LocalDateTime paymentTime;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
