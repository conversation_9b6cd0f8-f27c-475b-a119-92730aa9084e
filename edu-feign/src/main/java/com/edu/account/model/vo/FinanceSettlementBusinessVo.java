package com.edu.account.model.vo;

import com.edu.account.enums.FinanceSettlementBusinessTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算业务管理VO
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(value = "FinanceSettlementBusinessVo", description = "结算业务管理VO")
public class FinanceSettlementBusinessVo {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "结算批次ID")
    private Long settlementBatchId;

    @ApiModelProperty(value = "平台ID")
    private Long pl_id;

    @ApiModelProperty(value = "组织ID")
    private Long org_id;

    @ApiModelProperty(value = "业务类型")
    private FinanceSettlementBusinessTypeEnum businessType;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    @ApiModelProperty(value = "平台名称")
    private String plName;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "业务类型ID")
    private Long businessBatchId;

    @ApiModelProperty(value = "业务类型名称")
    private String businessBatchName;

    @ApiModelProperty(value = "业务开始时间")
    private LocalDateTime businessStartTime;

    @ApiModelProperty(value = "业务结束时间")
    private LocalDateTime businessEndTime;


    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
