package com.edu.account.model.vo;

import com.edu.account.enums.FinanceSettlementOrgAuditStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算机构管理VO
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(value = "FinanceSettlementOrganizationVo", description = "结算机构管理VO")
public class FinanceSettlementOrganizationVo {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "结算批次ID")
    private Long settlementBatchId;

    @ApiModelProperty(value = "业务类型名称")
    private Long settlementBusinessId;

    @ApiModelProperty(value = "1级机构ID")
    private Long oneOrgId;

    @ApiModelProperty(value = "1级机构编码")
    private String oneOrgCode;

    @ApiModelProperty(value = "2级机构ID")
    private Long twoOrgId;

    @ApiModelProperty(value = "2级机构编码")
    private String twoOrgCode;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构别名")
    private String orgAlias;

    @ApiModelProperty(value = "应缴总金额")
    private BigDecimal totalShouldAmount;

    @ApiModelProperty(value = "实缴总金额")
    private BigDecimal totalPaidAmount;

    @ApiModelProperty(value = "返费比例（0-1之间的小数，支持4位小数）")
    private BigDecimal refundRate;

    @ApiModelProperty(value = "返费比例显示（百分比）")
    private String refundRateDisplay;

    @ApiModelProperty(value = "返费金额（自动计算）")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "审核状态")
    private FinanceSettlementOrgAuditStatusEnum auditStatus;

    @ApiModelProperty(value = "审核状态名称")
    private String auditStatusName;

    @ApiModelProperty(value = "审核意见")
    private String auditRemark;

    @ApiModelProperty(value = "审核人姓名")
    private String auditByName;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "学员数量")
    private Integer studentCount;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
