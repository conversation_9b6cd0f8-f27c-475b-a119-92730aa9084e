package com.edu.account.model.vo;

import com.edu.account.domain.ScoreList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ScoreListVo", description = "学生成绩vo")
@SuperBuilder
public class ScoreListVo extends ScoreList {

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "年级")
    private String gradeName;

    @ApiModelProperty(value = "学号")
    private String studentNumber;

    @ApiModelProperty(value = "准考证号")
    private String examRegNumber;

    @ApiModelProperty(value = "考核批次名称")
    private String processExamBatchName;

    @ApiModelProperty(value = "考核批次Id")
    private Long processExamBatchId;

    @ApiModelProperty(value = "所属平台")
    private String platName;

    @ApiModelProperty(value = "所属组织")
    private String orgName;

    @ApiModelProperty(value = "报考学校")
    private String schoolName;

    @ApiModelProperty(value = "报考专业")
    private String specialtyName;

    @ApiModelProperty(value = "层次名称")
    private String levelName;

    @ApiModelProperty(value = "科目名称")
    private String subName;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "独立考试名称")
    private String aloneExamName;

    @ApiModelProperty(value = "综合成绩统计数")
    private Integer examCount;

    @ApiModelProperty(value = "在校成绩综合成绩")
    private BigDecimal synthesizeScore;

    @ApiModelProperty(value = "课程代码", name = "subCode")
    private String subCode;
    @ApiModelProperty(value = "课程名称", name = "name")
    private String name;
    @ApiModelProperty(value = "学分", name = "credit")
    private Integer credit;
    @ApiModelProperty(value = "教学形式", name = "teachingForm")
    private String teachingForm;
    @ApiModelProperty(value = "课程类别", name = "subType")
    private String subType;

    @ApiModelProperty(value = "数据来源", name = "dataSources")
    private String dataSources;

    @ApiModelProperty(value = "性别")
    private String sex;
}
