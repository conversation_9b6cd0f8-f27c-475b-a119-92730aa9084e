package com.edu.account.model.query;

import com.edu.account.enums.ScoreListTypeEnum;
import com.edu.adult.enums.StudentRollStatusEnum;
import com.edu.commons.model.commons.query.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
*  <AUTHOR>
*  @DESCRIPTION 成绩管理
*  @email <EMAIL>
*  2023-10-31
*/

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ScoreListQuery", description = "成绩管理")
public class ScoreListQuery extends BaseQuery implements Serializable {
    @ApiModelProperty(value = "主键id", name = "id")
    private Long id;

    @ApiModelProperty(value = "平台id", name = "plId")
    private Long plId;

    @ApiModelProperty(value = "组织code", name = "orgCode")
    private String  orgCode;

    @ApiModelProperty(value = "年级id", name = "gradeId")
    private Long gradeId;

    @ApiModelProperty(value = "专业id", name = "specialtyId")
    private Long specialtyId;

    @ApiModelProperty(value = "层次id", name = "levelId")
    private Long levelId;

    @ApiModelProperty(value = "手机号", name = "phone")
    private String phone;

    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    @ApiModelProperty(value = "学籍状态:" +
            "    REGISTER(注册学籍)," +
            "    REPORT(报道入学)," +
            "    NOT_REGISTER(未注册)," +
            "    GRADUATION(已毕业)," +
            "    GIVE_UP(放弃学籍)," +
            "    AUTOMATIC_DROPOUT(自动退学)," +
            "    IN(在籍)," +
            "    DROPOUT(退学)," +
            "    SUSPENDED(休学)")
    private StudentRollStatusEnum studentRollState;

    @ApiModelProperty(value = "录入状态：0-未录入，1-已录入")
    private Integer entryState;

    @ApiModelProperty(value = "学期")
    private Integer gradeTerm;

    @ApiModelProperty(value = "统考批次id", name = "computerBatchId")
    private Long unifiedProcessExamBatchId;

    @ApiModelProperty(value = "省考批次id", name = "computerBatchId")
    private Long computerBatchId;

    @ApiModelProperty(value = "类型：" +
            "    FINAL_EXAM(在校)," +
            "    ALONE_EXAM(独立)," +
            "    UNIFIED_EXAM(统考)," +
            "    COMPUTER_EXAM(省考)",required = true)
    @NotNull
    private ScoreListTypeEnum type;

    @ApiModelProperty(value = "是否发布0-待发布，1-已发布", name = "isPublish")
    private Integer isPublish;

    @ApiModelProperty(value = "选中导出的ID", name = "ids")
    private List<Long> ids;

    @ApiModelProperty(value = "变更成绩", name = "changeGrades")
    private BigDecimal changeGrades;

    @ApiModelProperty(value = "审核状态: 0:待审核 1:审核不通过 2:审核通过", name = "auditStatus")
    private Integer auditStatus;

    @ApiModelProperty(value = "录取大表中的ID", name = "enrollStudentIds")
    private List<Long> enrollStudentIds;
}
