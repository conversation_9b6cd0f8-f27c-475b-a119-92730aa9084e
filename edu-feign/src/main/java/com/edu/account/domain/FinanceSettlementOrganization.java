package com.edu.account.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.edu.account.enums.FinanceSettlementBusinessTypeEnum;
import com.edu.account.enums.FinanceSettlementOrgAuditStatusEnum;
import com.edu.commons.entity.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算机构管理（批次下级功能）
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(value = "FinanceSettlementOrganization", description = "结算机构管理")
@TableName("ua_finance_settlement_organization")
public class FinanceSettlementOrganization extends BaseDomain implements Serializable {

    @ApiModelProperty(value = "结算批次ID", name = "settlementBatchId")
    @TableField("settlement_batch_id")
    private Long settlementBatchId;

    @ApiModelProperty(value = "业务类型", name = "settlementBusinessId")
    @TableField("settlement_business_id")
    private Long settlementBusinessId;

    @ApiModelProperty(value = "1级机构ID", name = "oneOrgId")
    @TableField("one_org_id")
    private Long oneOrgId;

    @ApiModelProperty(value = "1级组织代码", name = "oneOrgCode")
    @TableField("one_org_code")
    private String oneOrgCode;

    @ApiModelProperty(value = "2级机构ID", name = "twoOrgId")
    @TableField("two_org_id")
    private Long twoOrgId;

    @ApiModelProperty(value = "2级组织代码", name = "twoOrgCode")
    @TableField("two_org_code")
    private String twoOrgCode;

    @ApiModelProperty(value = "机构名称", name = "orgName")
    @TableField("org_name")
    private String orgName;

    @ApiModelProperty(value = "机构别名", name = "orgAlias")
    @TableField("org_alias")
    private String orgAlias;

    @ApiModelProperty(value = "应缴总金额", name = "totalShouldAmount")
    @TableField("total_should_amount")
    private BigDecimal totalShouldAmount;

    @ApiModelProperty(value = "实缴总金额", name = "totalPaidAmount")
    @TableField("total_paid_amount")
    private BigDecimal totalPaidAmount;

    @ApiModelProperty(value = "返费比例（0-1之间的小数，支持4位小数）", name = "refundRate")
    @TableField("refund_rate")
    private BigDecimal refundRate;

    @ApiModelProperty(value = "返费金额（自动计算）", name = "refundAmount")
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "审核状态", name = "auditStatus")
    @TableField("audit_status")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核意见", name = "auditRemark")
    @TableField("audit_remark")
    private String auditRemark;

    @ApiModelProperty(value = "审核人ID", name = "auditBy")
    @TableField("audit_by")
    private Long auditBy;

    @ApiModelProperty(value = "审核时间", name = "auditTime")
    @TableField("audit_time")
    private java.time.LocalDateTime auditTime;

    @ApiModelProperty(value = "描述", name = "description")
    @TableField("description")
    private String description;

    // 非数据库字段
    @ApiModelProperty(value = "业务类型名称")
    @TableField(exist = false)
    private String businessTypeName;

    @ApiModelProperty(value = "审核状态名称")
    @TableField(exist = false)
    private String auditStatusName;

    @ApiModelProperty(value = "审核人姓名")
    @TableField(exist = false)
    private String auditByName;

    @ApiModelProperty(value = "返费比例显示（百分比）")
    @TableField(exist = false)
    private String refundRateDisplay;
}
