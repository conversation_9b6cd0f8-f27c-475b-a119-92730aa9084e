package com.edu.account.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.edu.commons.entity.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算学员明细管理
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(value = "FinanceSettlementStudent", description = "结算学员明细管理")
@TableName("ua_finance_settlement_student")
public class FinanceSettlementStudent extends BaseDomain implements Serializable {

    @ApiModelProperty(value = "结算批次ID", name = "settlementBatchId")
    @TableField("settlement_batch_id")
    private Long settlementBatchId;

    @ApiModelProperty(value = "结算业务ID")
    @TableField("settlement_business_id")
    private Long settlementBusinessId;

    @ApiModelProperty(value = "1级机构ID", name = "oneOrgId")
    @TableField("one_org_id")
    private Long oneOrgId;

    @ApiModelProperty(value = "1级组织代码", name = "oneOrgCode")
    @TableField("one_org_code")
    private String oneOrgCode;

    @ApiModelProperty(value = "2级机构ID", name = "twoOrgId")
    @TableField("two_org_id")
    private Long twoOrgId;

    @ApiModelProperty(value = "2级组织代码", name = "twoOrgCode")
    @TableField("two_org_code")
    private String twoOrgCode;

    @ApiModelProperty(value = "录取大表ID", name = "enrollId")
    @TableField("enroll_id")
    private Long enrollId;

    @ApiModelProperty(value = "学员ID", name = "studentId")
    @TableField("student_id")
    private Long studentId;

    @ApiModelProperty(value = "学员姓名", name = "studentName")
    @TableField("student_name")
    private String studentName;

    @ApiModelProperty(value = "身份证号", name = "idNum")
    @TableField("id_num")
    private String idNum;

    @ApiModelProperty(value = "专业ID", name = "specialtyId")
    @TableField("specialty_id")
    private String specialtyId;

    @ApiModelProperty(value = "层次ID", name = "levelId")
    @TableField("level_id")
    private String levelId;

    @ApiModelProperty(value = "缴费年度", name = "paymentYear")
    @TableField("payment_year")
    private String paymentYear;

    @ApiModelProperty(value = "缴费学年", name = "paymentAcademicYear")
    @TableField("payment_academic_year")
    private String paymentAcademicYear;

    @ApiModelProperty(value = "应缴金额", name = "shouldAmount")
    @TableField("should_amount")
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "实缴金额", name = "paidAmount")
    @TableField("paid_amount")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "返费金额", name = "refundAmount")
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "缴费订单ID", name = "paymentOrderId")
    @TableField("payment_order_id")
    private Long paymentOrderId;

    @ApiModelProperty(value = "缴费时间", name = "paymentTime")
    @TableField("payment_time")
    private java.time.LocalDateTime paymentTime;

    @ApiModelProperty(value = "支付状态", name = "paymentStatus")
    @TableField("payment_status")
    private Integer paymentStatus;

    @ApiModelProperty(value = "描述", name = "description")
    @TableField("description")
    private String description;
}
