package com.edu.adult.model.vo.shcool.pc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class StudentAdultStudyVo implements Serializable {
    @ApiModelProperty(value = "id", name = "id")
    private Long id;

    @ApiModelProperty(value = "平台ID", name = "plId")
    private Long plId;

    @ApiModelProperty(value = "一级组织ID", name = "oneOrgId")
    private Long oneOrgId;

    @ApiModelProperty(value = "二级组织ID", name = "twoOrgId")
    private Long twoOrgId;

    @ApiModelProperty(value = "平台名称", name = "platName")
    private String platName;

    @ApiModelProperty(value = "学员id", name = "studentId")
    private Long studentId;

    @ApiModelProperty(value = "学号", name = "studentId")
    private String studentNumber;

    @ApiModelProperty(value = "姓名", name = "studentName")
    private String studentName;

    @ApiModelProperty(value = "专业id", name = "specialtyId")
    private Long specialtyId;

    @ApiModelProperty(value = "专业名称", name = "specialtyName")
    private String specialtyName;

    @ApiModelProperty(value = "一级组织编码", name = "oneOrgCode")
    private String oneOrgCode;

    @ApiModelProperty(value = "一级组织名称", name = "oneOrgName")
    private String oneOrgName;

    @ApiModelProperty(value = "二级组织编码", name = "twoOrgCode")
    private String twoOrgCode;

    @ApiModelProperty(value = "二级组织名称", name = "twoOrgName")
    private String twoOrgName;

    @ApiModelProperty(value = "身份证号", name = "idNumber")
    private String idNumber;

    @ApiModelProperty(value = "学员批次Id", name = "batchId")
    private Long batchId;

    @ApiModelProperty(value = "学员批次名称", name = "batchName")
    private String batchName;

    @ApiModelProperty(value = "年级名称", name = "gradeName")
    private String gradeName;

    @ApiModelProperty(value = "层级Id", name = "levelId")
    private String levelId;

    @ApiModelProperty(value = "层级名称", name = "levelName")
    private String levelName;

    @ApiModelProperty(value = "学习形式", name = "studyType")
    private String studyType;

    @ApiModelProperty(value = "学科")
    private String subName;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程ID")
    private Long courseId;

    @ApiModelProperty(value = "学员批次id", name = "studentBatchId")
    private Long studentBatchId;

    @ApiModelProperty(value = "性别", name = "sex")
    private String sex;

    @ApiModelProperty(value = "民族", name = "nation")
    private String nation;
    @ApiModelProperty(value = "学制", name = "schoolSys")
    private BigDecimal schoolSys;
    @ApiModelProperty(value = "学期", name = "termName")
    private Integer termNum;

    @ApiModelProperty(value = "学期开始时间", name = "termStart")
    private Date termStart;

    @ApiModelProperty(value = "学期结束日期", name = "termEnd")
    private Date termEnd;

    @ApiModelProperty(value = "综合成绩", name = "compositeScore")
    private BigDecimal compositeScore;
}
