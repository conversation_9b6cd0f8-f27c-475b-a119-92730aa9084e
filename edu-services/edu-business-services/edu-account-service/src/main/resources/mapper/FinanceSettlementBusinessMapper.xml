<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.account.mapper.FinanceSettlementBusinessMapper">

    <!-- 结果映射 -->
    <resultMap id="SettlementBusinessVoMap" type="com.edu.account.model.vo.FinanceSettlementBusinessVo">
        <id column="id" property="id"/>
        <result column="business_type" property="businessType"/>
        <result column="business_type_name" property="businessTypeName"/>
        <result column="pl_name" property="plName"/>
        <result column="org_name" property="orgName"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询结算业务列表 -->
    <select id="selectQuery" resultType="com.edu.account.model.vo.FinanceSettlementBusinessVo">
        SELECT
        sb.id,
        sb.settlement_batch_id,
        sb.business_type,
        CASE sb.business_type
        WHEN 'PLATFORM_FEE_SETTLEMENT' THEN '平台费结算'
        WHEN 'ADVANCE_ENROLLMENT_FEE_SETTLEMENT' THEN '预报名费用结算'
        ELSE sb.business_type
        END as business_type_name,
        sb.pl_id,
        getPlatName(fsb.pl_id) as pl_name,
        sb.org_code,
        getOrgName(fsb.org_code) as org_name,
        sb.business_batch_id,
        sb.business_batch_name,
        sb.business_start_time,
        sb.business_end_time,
        sb.description,
        sb.create_time,
        sb.update_time
        FROM ua_finance_settlement_business sb
        inner join ua_finance_settlement_batch fsb on fsb.id = sb.settlement_batch_id
        WHERE sb.del = 0
        <if test="settlementBatchId != null">
            AND sb.settlement_batch_id = #{settlementBatchId}
        </if>
        <if test="businessType != null and businessType != ''">
            AND sb.business_type = #{businessType}
        </if>
        <if test="keyName != null and keyName != ''">
            AND sb.business_batch_name like CONCAT('%', #{keyName} , '%')
        </if>
        ORDER BY sb.id DESC
    </select>

    <!-- 根据结算批次ID查询业务列表 -->
    <select id="selectBySettlementBatchId" resultType="com.edu.account.model.vo.FinanceSettlementBusinessVo">
        SELECT 
            sb.id,
            sb.settlement_batch_id,
            sb.pl_id,
            sb.org_id,
            sb.business_type,
            CASE sb.business_type
                WHEN 'PLATFORM_FEE_SETTLEMENT' THEN '平台费结算'
                WHEN 'ADVANCE_ENROLLMENT_FEE_SETTLEMENT' THEN '预报名费用结算'
                ELSE sb.business_type
            END as business_type_name,
            sb.org_code,
            sb.business_batch_id,
            sb.business_batch_name,
            sb.business_start_time,
            sb.business_end_time,
            sb.description,
            sb.create_time,
            sb.update_time
        FROM ua_finance_settlement_business sb
        WHERE sb.settlement_batch_id = #{settlementBatchId}
          AND sb.del = 0
        ORDER BY sb.id DESC
    </select>

    <!-- 根据业务类型和批次ID查询业务列表 -->
    <select id="selectByBatchIdAndBusinessType" resultType="com.edu.account.domain.FinanceSettlementBusiness">
        SELECT *
        FROM ua_finance_settlement_business
        WHERE settlement_batch_id = #{settlementBatchId}
          AND business_type = #{businessType}
          AND del = 0
        ORDER BY create_time DESC
    </select>



    <!-- 批量删除结算业务 -->
    <update id="batchDeleteByIds">
        UPDATE ua_finance_settlement_business
        SET del = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据预报名批次ID查询业务 -->
    <select id="selectByPreEnrollmentBatchId" resultType="com.edu.account.domain.FinanceSettlementBusiness">
        SELECT *
        FROM ua_finance_settlement_business
        WHERE business_batch_id = #{businessBatchId}
          AND del = 0
        ORDER BY create_time DESC
    </select>
    <!-- 根据业务类型和平台ID获取业务批次数据 -->
    <select id="getBusinessBatchData" resultType="com.edu.account.model.vo.FinanceSettlementBusinessListDataVo">
        <choose>
            <!-- 平台费结算 - 成教平台 (pl_id = 2) -->
            <when test="businessType == 'PLATFORM_FEE_SETTLEMENT' and plId == 2">
                SELECT
                    ug.id as businessBatchId,
                    ug.pl_id,
                    ug.org_id,
                    ug.grade_name as businessBatchName,
                    (SELECT MIN(ugt.term_start) FROM ua_grade_term ugt WHERE ugt.grade_id = ug.id) as start_time,
                    (SELECT MAX(ugt.term_end) FROM ua_grade_term ugt WHERE ugt.grade_id = ug.id) as end_time
                FROM ua_grade ug
                WHERE ug.pl_id = #{plId}
                  AND ug.org_id = #{orgId}
                  AND ug.del = 0
            </when>

            <!-- 平台费结算 - 自考平台 (pl_id = 3) -->
            <when test="businessType == 'PLATFORM_FEE_SETTLEMENT' and plId == 3">
                SELECT DISTINCT
                    usb.id as businessBatchId,
                    sess.pl_id,
                    sess.one_org_id as org_id,
                    usb.batch_name as businessBatchName
                FROM ss_enroll_student_self sess
                INNER JOIN ua_student_batch usb ON sess.student_batch_id = usb.id
                WHERE sess.pl_id = #{plId}
                  AND sess.one_org_id = #{orgId}
                  AND sess.del = 0
            </when>

            <!-- 预报名费用结算 - 成教平台 (pl_id = 2) -->
            <when test="businessType == 'ADVANCE_ENROLLMENT_FEE_SETTLEMENT' and plId == 2">
                SELECT
                    ubeb.id as businessBatchId,
                    ubeb.pl_id,
                    ubeb.org_id,
                    ubeb.name as businessBatchName,
                    ubeb.apply_start_time as start_time,
                    ubeb.apply_end_time as end_time
                FROM ua_beforehand_enroll_bacth ubeb
                WHERE ubeb.pl_id = #{plId}
                  AND ubeb.org_id = #{orgId}
                  AND ubeb.del = 0
            </when>

            <!-- 预报名费用结算 - 自考平台 (pl_id = 3) -->
            <when test="businessType == 'ADVANCE_ENROLLMENT_FEE_SETTLEMENT' and plId == 3">
                SELECT
                    ubeb.id businessBatchId,
                    ubeb.pl_id,
                    ubeb.org_id,
                    ubeb.name businessBatchName,
                    ubeb.apply_start_time as start_time,
                    ubeb.apply_end_time as end_time
                FROM ua_beforehand_enroll_bacth ubeb
                WHERE ubeb.pl_id = #{plId}
                  AND ubeb.org_id = #{orgId}
                  AND ubeb.del = 0
            </when>

            <!-- 默认情况 - 返回空结果 -->
            <otherwise>
                SELECT NULL as id, NULL as pl_id, NULL as org_id, NULL as name WHERE 1 = 0
            </otherwise>
        </choose>
    </select>
</mapper>
