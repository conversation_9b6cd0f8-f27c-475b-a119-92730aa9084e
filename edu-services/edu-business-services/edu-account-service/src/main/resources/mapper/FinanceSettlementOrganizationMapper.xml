<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.account.mapper.FinanceSettlementOrganizationMapper">

    <!-- 结果映射 -->
    <resultMap id="SettlementOrganizationVoMap" type="com.edu.account.model.vo.FinanceSettlementOrganizationVo">
        <id column="id" property="id"/>
        <result column="settlement_batch_id" property="settlementBatchId"/>
        <result column="settlement_business_id" property="settlementBusinessId"/>
        <result column="org_name" property="orgName"/>
        <result column="org_alias" property="orgAlias"/>
        <result column="total_should_amount" property="totalShouldAmount"/>
        <result column="total_paid_amount" property="totalPaidAmount"/>
        <result column="refund_rate" property="refundRate"/>
        <result column="refund_rate_display" property="refundRateDisplay"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_status_name" property="auditStatusName"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="audit_by_name" property="auditByName"/>
        <result column="audit_time" property="auditTime"/>

        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询结算机构列表 -->
    <select id="selectQuery" resultType="com.edu.account.model.vo.FinanceSettlementOrganizationVo">
        SELECT
        so.id,
        so.settlement_batch_id,
        so.settlement_business_id,
        so.one_org_id,
        so.one_org_code,
        so.two_org_id,
        so.two_org_code,
        getOrgName(so.two_org_code) as org_name,
        so.org_alias,
        so.total_should_amount,
        so.total_paid_amount,
        so.refund_rate,
        CONCAT(ROUND(so.refund_rate * 100, 2), '%') as refund_rate_display,
        so.refund_amount,
        so.audit_status,
        CASE so.audit_status
        WHEN 0 THEN '待审核'
        WHEN 2 THEN '已审核'
        WHEN 10 THEN '审核拒绝'
        ELSE '未知'
        END as audit_status_name,
        so.audit_remark,
        su.nickname as audit_by_name,
        so.audit_time,
        so.description,
        so.create_time,
        so.update_time
        FROM ua_finance_settlement_organization so
        LEFT JOIN sys_user su ON su.id = so.audit_by
        WHERE so.del = 0
        <if test="settlementBatchId != null">
            AND so.settlement_batch_id = #{settlementBatchId}
        </if>
        <if test="keyName!=null and keyName!=''">
            AND so.org_name like CONCAT('%', #{keyName}, '%')
        </if>
        ORDER BY so.create_time DESC
    </select>

    <!-- 查询结算机构列表 -->
    <select id="selectQueryVo" resultType="com.edu.account.model.vo.FinanceSettlementOrganizationVo">
        SELECT
        so.id,
        so.settlement_batch_id,
        so.settlement_business_id,
        so.one_org_id,
        so.one_org_code,
        so.two_org_id,
        so.two_org_code,
        so.org_name,
        so.org_alias,
        so.total_should_amount,
        so.total_paid_amount,
        so.refund_rate,
        CONCAT(ROUND(so.refund_rate * 100, 2), '%') as refund_rate_display,
        so.refund_amount,
        so.audit_status,
        CASE so.audit_status
        WHEN 0 THEN '待审核'
        WHEN 2 THEN '已审核'
        WHEN 10 THEN '审核拒绝'
        ELSE '未知'
        END as audit_status_name,
        so.audit_remark,
        su.nickname as audit_by_name,
        so.audit_time,
        so.description,
        so.create_time,
        so.update_time
        FROM ua_finance_settlement_organization so
        LEFT JOIN sys_user su ON su.id = so.audit_by
        WHERE so.del = 0
        <if test="settlementBatchId != null">
            AND so.settlement_batch_id = #{settlementBatchId}
        </if>
        <if test="keyName!=null and keyName!=''">
            AND so.org_name like CONCAT('%', #{keyName}, '%')
        </if>
        ORDER BY so.create_time DESC
    </select>

    <!-- 根据结算批次ID查询机构列表 -->
    <select id="selectBySettlementBatchId" resultType="com.edu.account.model.vo.FinanceSettlementOrganizationVo">
        SELECT so.id,
               so.settlement_batch_id,
               so.settlement_business_id,
               so.one_org_code,
               so.two_org_code,
               so.org_name,
               so.org_alias,
               so.total_should_amount,
               so.total_paid_amount,
               so.refund_rate,
               CONCAT(ROUND(so.refund_rate * 100, 2), '%') as refund_rate_display,
               so.refund_amount,
               so.audit_status,
               CASE so.audit_status
                   WHEN 0 THEN '待审核'
                   WHEN 2 THEN '已审核'
                   WHEN 10 THEN '审核拒绝'
                   ELSE '未知'
                   END                                     as audit_status_name,
               so.audit_remark,
               su.nickname                                 as audit_by_name,
               so.audit_time,
               so.description,
               so.create_time,
               so.update_time
        FROM ua_finance_settlement_organization so
                 LEFT JOIN sys_user su ON su.id = so.audit_by
        WHERE so.settlement_batch_id = #{settlementBatchId}
          AND so.del = 0
        ORDER BY so.create_time DESC
    </select>

    <!-- 批量设置返费比例 -->
    <update id="batchSetRefundRate">
        UPDATE ua_finance_settlement_organization
        SET refund_rate = #{refundRate},
            update_time = NOW()
        WHERE id IN
        <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
        AND del = 0
    </update>

    <!-- 计算返费金额 -->
    <update id="calculateRefundAmount">
        UPDATE ua_finance_settlement_organization
        SET refund_amount = ROUND(total_paid_amount * refund_rate, 2),
            update_time = NOW()
        WHERE id = #{orgId}
          AND del = 0
    </update>

    <!-- 批量计算返费金额 -->
    <update id="batchCalculateRefundAmount">
        UPDATE ua_finance_settlement_organization
        SET refund_amount = ROUND(total_paid_amount * refund_rate, 2),
            update_time = NOW()
        WHERE settlement_batch_id = #{settlementBatchId}
          AND del = 0
    </update>

    <!-- 批量审核机构 -->
    <update id="batchAuditOrganization">
        UPDATE ua_finance_settlement_organization
        SET audit_status = #{auditStatus},
            audit_remark = #{auditRemark},
            audit_by = #{auditBy},
            audit_time = NOW(),
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del = 0
    </update>

    <!-- 更新机构统计信息 -->
    <update id="updateOrganizationStatistics">
        UPDATE ua_finance_settlement_organization
        SET student_count = #{studentCount},
            total_should_amount = #{totalShouldAmount},
            total_paid_amount = #{totalPaidAmount},
            refund_amount = ROUND(#{totalPaidAmount} * refund_rate, 2),
            update_time = NOW()
        WHERE id = #{orgId}
    </update>

    <!-- 批量更新机构统计信息 -->
    <update id="batchUpdateOrganizationStatistics">
        UPDATE ua_finance_settlement_organization so
        INNER JOIN (
            SELECT
                ssd.settlement_org_id,
                COUNT(ssd.id) as student_count,
                COALESCE(SUM(ssd.should_amount), 0) as total_should_amount,
                COALESCE(SUM(ssd.paid_amount), 0) as total_paid_amount
            FROM ua_settlement_student_detail ssd
            WHERE ssd.del = 0
            GROUP BY ssd.settlement_org_id
        ) stats ON stats.settlement_org_id = so.id
        SET so.student_count = stats.student_count,
            so.total_should_amount = stats.total_should_amount,
            so.total_paid_amount = stats.total_paid_amount,
            so.refund_amount = ROUND(stats.total_paid_amount * so.refund_rate, 2),
            so.update_time = NOW()
        WHERE so.settlement_batch_id = #{settlementBatchId}
          AND so.del = 0
    </update>

    <!-- 批量删除结算机构 -->
    <update id="batchDeleteByIds">
        UPDATE ua_finance_settlement_organization
        SET del = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据审核状态查询机构列表 -->
    <select id="selectByAuditStatus" resultType="com.edu.account.domain.FinanceSettlementOrganization">
        SELECT *
        FROM ua_finance_settlement_organization
        WHERE settlement_batch_id = #{settlementBatchId}
          AND audit_status = #{auditStatus}
          AND del = 0
        ORDER BY create_time DESC
    </select>

    <!-- 检查机构是否已存在 -->
    <select id="checkOrganizationExists" resultType="int">
        SELECT COUNT(1)
        FROM ua_finance_settlement_organization
        WHERE settlement_batch_id = #{settlementBatchId}
          AND org_id = #{orgId}
          AND del = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
