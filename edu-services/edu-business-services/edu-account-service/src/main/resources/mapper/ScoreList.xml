<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.account.mapper.ScoreListMapper">

    <resultMap id="BaseResultMap" type="com.edu.account.domain.ScoreList">
        <id column="id" property="id"/>
        <result column="student_id" property="studentId"/>
        <result column="enroll_student_id" property="enrollStudentId"/>
        <result column="grade_id" property="gradeId"/>
        <result column="pl_id" property="plId"/>
        <result column="org_id" property="orgId"/>
        <result column="org_code" property="orgCode"/>
        <result column="school_id" property="schoolId"/>
        <result column="unified_process_exam_batch_id" property="unifiedProcessExamBatchId"/>
        <result column="computer_batch_id" property="computerBatchId"/>
        <result column="specialty_id" property="specialtyId"/>
        <result column="level_id" property="levelId"/>
        <result column="alone_exam_id" property="aloneExamId"/>
        <result column="sub_id" property="subId"/>
        <result column="course_id" property="courseId"/>
        <result column="grade_term_id" property="gradeTermId"/>
        <result column="type" property="type" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="is_publish" property="isPublish"/>
        <result column="plan_id" property="planId"/>
        <result column="del" property="del"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="student_test_Id" property="studentTestId"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,student_id,plan_id,grade_id,pl_id,org_id,org_code,school_id,enroll_student_id,unified_process_exam_batch_id,computer_batch_id,specialty_id,level_id,alone_exam_id,sub_id,course_id,grade_term_id,exam_score,type,is_publish,del,remark,create_by,update_by,create_time,update_time,student_test_Id
    </sql>

    <resultMap id="ScoreListVO" type="com.edu.account.model.vo.ScoreListVo" extends="BaseResultMap">
        <result column="id_number" property="idNumber"/>
        <result column="sub_name" property="subName"/>
        <result column="phone" property="phone"/>
        <result column="grade_name" property="gradeName"/>
        <result column="level_name" property="levelName"/>
        <result column="course_name" property="courseName"/>
        <result column="student_name" property="studentName"/>
        <result column="student_number" property="studentNumber"/>
        <result column="exam_reg_number" property="examRegNumber"/>
        <result column="specialty_name" property="specialtyName"/>
        <result column="process_exam_batch_name" property="processExamBatchName"/>
        <result column="process_exam_batch_id" property="processExamBatchId"/>
        <result column="alone_exam_name" property="aloneExamName"/>
        <result column="exam_count" property="examCount" />
        <result column="synthesize_score" property="synthesizeScore"/>
        <result column="data_sources" property="dataSources"/>
        <result column="sub_code" property="subCode"/>
    </resultMap>
    <select id="selectRelationshipQuery" resultType="java.lang.Long">
        select sl.id from ua_score_list sl
        <choose>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@FINAL_EXAM">
                join ae_enroll_student_adult as enroll on sl.enroll_student_id = enroll.id
                join ua_grade as g on sl.grade_id = g.id
                LEFT JOIN (
                select score_list_id,IF(IFNULL(SUM(details.exam_score),0) > 0,1,0) exam_score from ua_score_list_details as details where details.del = 0 GROUP BY score_list_id
                ) ua on ua.score_list_id = sl.id
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@ALONE_EXAM">
                JOIN le_student_test AS test ON sl.student_test_id = test.id
                join le_alone_exam as ae on sl.alone_exam_id = ae.id
                left join (
                SELECT stu.student_id,'CJ' student_type,  stu.student_name, stu.id_number, stu.phone ,g.grade_name as gradeName
                FROM ae_enroll_student_adult stu LEFT JOIN ua_grade g on g.id = stu.grade_id
                union
                SELECT student_id, 'ZK' student_type, student_name, id_number, phone ,'' as gradeName FROM ss_enroll_student_self
                union
                SELECT student_id,'ZB' student_type, student_name, id_number, phone ,'' as gradeName FROM ua_student_roll_junior
                ) enroll on sl.student_id = enroll.student_id and test.student_type = enroll.student_type
                LEFT JOIN (select score_list_id,IF(IFNULL(SUM(details.exam_score),0) > 0,1,0) exam_score from ua_score_list_details as details where details.del = 0 GROUP BY score_list_id
                ) ua on ua.score_list_id = sl.id
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@UNIFIED_EXAM">
                join ss_enroll_student_self as enroll on sl.enroll_student_id = enroll.id
                LEFT JOIN (
                select score_list_id,IF(IFNULL(SUM(details.exam_score),0) > 0,1,0) exam_score from ua_score_list_details as details where details.del = 0 GROUP BY score_list_id
                ) ua on ua.score_list_id = sl.id
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@COMPUTER_EXAM">
                join ss_enroll_student_self as enroll on sl.enroll_student_id = enroll.id
                LEFT JOIN (
                select score_list_id,IF(IFNULL(SUM(details.exam_score),0) > 0,1,0) exam_score from ua_score_list_details as details where details.del = 0 GROUP BY score_list_id
                ) ua on ua.score_list_id = sl.id
            </when>
        </choose>
        where sl.del = 0
        <if test="query.keyName != null and query.keyName != ''">
            and (enroll.student_name like CONCAT(#{query.keyName},'%')
            or enroll.id_number like CONCAT(#{query.keyName},'%')
            )
        </if>
        <if test="query.plId != null">
            and sl.pl_id = #{query.plId}
        </if>
        <if test="query.orgCode != null and query.type == @com.edu.account.enums.ScoreListTypeEnum@ALONE_EXAM">
            and sl.org_code like CONCAT(#{query.orgCode},'%')
        </if>
        <if test="query.orgCode != null and query.type != @com.edu.account.enums.ScoreListTypeEnum@ALONE_EXAM">
            and( sl.org_code like CONCAT(#{query.orgCode},'%') or enroll.two_org_code = #{query.orgCode})
        </if>
        <if test="query.gradeId != null">
            and sl.grade_id = #{query.gradeId}
        </if>
        <if test="query.specialtyId != null">
            and sl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.levelId">
            and sl.level_id = #{query.levelId}
        </if>
        <if test="query.phone != null and query.phone != ''">
            and enroll.phone like CONCAT(#{query.phone},'%')
        </if>
        <if test="query.idNumber != null and query.idNumber != ''">
            and enroll.id_number like  CONCAT(#{query.idNumber},'%')
        </if>
        <if test="query.studentRollState != null">
            and enroll.student_roll_state = #{query.studentRollState}
        </if>
        <if test="query.entryState != null">
            and ua.exam_score = 1 = #{query.entryState}
        </if>
        <if test="query.gradeTerm != null">
            and sl.grade_term_id = #{query.gradeTerm}
        </if>
        <if test="query.unifiedProcessExamBatchId != null">
            and sl.unified_process_exam_batch_id = #{query.unifiedProcessExamBatchId}
        </if>
        <if test="query.computerBatchId != null">
            and sl.computer_batch_id = #{query.computerBatchId}
        </if>
        <if test="query.type != null">
            and sl.type = #{query.type}
        </if>
        <if test="query.isPublish != null">
            and sl.is_publish = #{query.isPublish}
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            and sl.id in
            <foreach collection="query.ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY sl.id ORDER BY sl.id DESC
    </select>
    <select id="selectQuery" resultMap="ScoreListVO">
        select
        sl.* , s.spe_name as specialty_name ,count(ua.id) exam_count,
        CASE WHEN max(ua.data_sources)  = '导入成绩' THEN '导入成绩' ELSE '线上成绩' END AS data_sources,
        <choose>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@FINAL_EXAM">
                enroll.student_name, enroll.id_number, enroll.student_number,sub.sub_name,
                c.name as course_name, g.grade_name as grade_name,IFNULL(ua.composite_score,0) synthesize_score
            </when>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@ALONE_EXAM">
                enroll.student_name as student_name, enroll.id_number as id_number , enroll.phone as phone,
                enroll.gradeName as grade_name, ae.exam_name as alone_exam_name, c.name as course_name
                , sub.sub_name as sub_name
            </when>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@UNIFIED_EXAM">
                enroll.student_name as student_name, enroll.id_number as id_number , enroll.exam_reg_number as exam_reg_number,
                upeb.bacth_name as process_exam_batch_name, upeb.id as processExamBatchId, l.level_name as level_name
            </when>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@COMPUTER_EXAM">
                enroll.student_name as student_name, enroll.id_number as id_number , enroll.exam_reg_number as exam_reg_number,
                cb.batch_name as process_exam_batch_name, cb.id as processExamBatchId, l.level_name as level_name
            </when>
        </choose>
        from ua_score_list sl
        <choose>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@FINAL_EXAM">
                join ae_enroll_student_adult as enroll on sl.enroll_student_id = enroll.id
                join ua_grade as g on sl.grade_id = g.id
                left join le_course as c on sl.course_id = c.id
                left join le_subject as sub on c.sub_id = sub.id
                LEFT JOIN (select * from ua_score_list_details where del = 0 ORDER BY update_time asc limit 1) ua1 on ua1.score_list_id = sl.id
            </when>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@ALONE_EXAM">
                join le_student_test as test on sl.student_test_id = test.id
                left join (
                SELECT stu.student_id,'CJ' student_type,  stu.student_name, stu.id_number, stu.phone ,g.grade_name as gradeName
                FROM ae_enroll_student_adult stu LEFT JOIN ua_grade g on g.id = stu.grade_id
                union
                SELECT student_id, 'ZK' student_type, student_name, id_number, phone ,'' as gradeName FROM ss_enroll_student_self
                union
                SELECT student_id,'ZB' student_type, student_name, id_number, phone ,'' as gradeName FROM ua_student_roll_junior
                ) enroll on sl.student_id = enroll.student_id and test.student_type = enroll.student_type
                join le_alone_exam as ae on sl.alone_exam_id = ae.id
                left join le_course as c on sl.course_id = c.id
                left join le_subject as sub on c.sub_id = sub.id
            </when>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@UNIFIED_EXAM">
                join ss_enroll_student_self as enroll on sl.enroll_student_id = enroll.id
                left join ss_unified_process_exam_batch as upeb on sl.unified_process_exam_batch_id = upeb.id
                LEFT JOIN le_level AS l ON sl.level_id = l.id
            </when>
            <when test="type == @com.edu.account.enums.ScoreListTypeEnum@COMPUTER_EXAM">
                join ss_enroll_student_self as enroll on sl.enroll_student_id = enroll.id
                left join ss_computer_batch as cb on sl.computer_batch_id = cb.id
                left join le_level as l on sl.level_id = l.id
            </when>
        </choose>
        left join le_specialty as s on sl.specialty_id = s.id
        LEFT JOIN ua_score_list_details ua on ua.score_list_id = sl.id and ua.del = 0
        where
        <if test="ids != null and ids.size() > 0">
            sl.id in
            <foreach collection="ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test=" ids == null or ids.size() &lt;= 0" >
            sl.id = -9999
        </if>

        GROUP BY sl.id ORDER BY sl.id DESC

    </select>

    <select id="getInfoByUnifiedExam" resultMap="BaseResultMap">
        select
        sl.*
        from ua_score_list as sl
        join ua_platform as p on sl.pl_id = p.id
        join ss_enroll_student_self as ess on sl.enroll_student_id = ess.id
        join ss_unified_process_exam_batch as upeb on sl.unified_process_exam_batch_id = upeb.id
        join le_specialty as s on sl.specialty_id = s.id
        left join le_level as l on sl.level_id = l.id
        where
        sl.del = 0
        and sl.type = '${@com.edu.account.enums.ScoreListTypeEnum@UNIFIED_EXAM}'
        and p.platform_name = #{item.platName}
        and ess.student_name = #{item.studentName}
        and ess.id_number = #{item.idNum}
        and ess.exam_reg_number = #{item.examRegNumber}
        and upeb.bacth_name = #{item.unifiedProcessExamBatchName}
        and s.spe_name = #{item.specialtyName}
        <if test="item.levelName != null and item.levelName != ''">
            and l.level_name = #{item.levelName}
        </if>
        limit 1
    </select>

    <select id="getInfoByComputerExam" resultMap="BaseResultMap">
        select
        sl.*
        from ua_score_list as sl
        join ua_platform as p on sl.pl_id = p.id
        join ss_enroll_student_self as ess on sl.enroll_student_id = ess.id
        join ss_computer_batch as cb on sl.computer_batch_id = cb.id
        join le_specialty as s on sl.specialty_id = s.id
        left join le_level as l on sl.level_id = l.id
        where
        sl.del = 0
        and sl.type = '${@com.edu.account.enums.ScoreListTypeEnum@COMPUTER_EXAM}'
        and p.platform_name = #{item.platName}
        and ess.student_name = #{item.studentName}
        and ess.id_number = #{item.idNum}
        and ess.exam_reg_number = #{item.examRegNumber}
        and cb.batch_name = #{item.computerBatchName}
        and s.spe_name = #{item.specialtyName}
        <if test="item.levelName != null and item.levelName != ''">
            and l.level_name = #{item.levelName}
        </if>
        limit 1
    </select>
    <select id="selectDownExcelQuery" resultType="com.edu.account.excel.export.ScoreListExport">
        select
        sl.*,
        detail.*,
        CASE
        WHEN detail.is_pass = 1 THEN '合格'
        WHEN detail.is_pass = 0 THEN '不合格'
        END AS result,
        up.platform_name as platName,
        us.org_name as orgName,
        sub.sub_name as sub_name,
        lc.name as course_name,
        s.spe_name as specialty_name,
        (select COUNT(id) from ua_score_list_details where score_list_id = sl.id and del = 0) as exam_count
        <choose>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@FINAL_EXAM">
                , enroll.student_name as student_name
                , enroll.id_number as id_number
                , enroll.student_number as student_number
                , sub.sub_name as sub_name
                , c.name as course_name
                , g.grade_name as grade_name
                , (select IFNULL(composite_score,0) from ua_score_list_details where del = 0 and score_list_id = sl.id order by update_time desc limit 1) as synthesize_score
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@ALONE_EXAM">
                , enroll.student_name as student_name
                , enroll.id_number as id_number
                , enroll.phone as phone
                , ae.exam_name as alone_exam_name
                , c.name as course_name
                , g.grade_name as grade_name
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@UNIFIED_EXAM">
                , enroll.student_name as student_name
                , enroll.id_number as id_number
                , enroll.exam_reg_number as exam_reg_number
                , upeb.bacth_name as process_exam_batch_name
                , l.level_name as level_name
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@COMPUTER_EXAM">
                , enroll.student_name as student_name
                , enroll.id_number as id_number
                , enroll.exam_reg_number as exam_reg_number
                , cb.batch_name as process_exam_batch_name
                , l.level_name as level_name
            </when>
        </choose>
        from ua_score_list as sl
        <choose>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@FINAL_EXAM">
                join ae_enroll_student_adult as enroll on sl.enroll_student_id = enroll.id
                join ua_grade as g on sl.grade_id = g.id
                left join le_course as c on sl.course_id = c.id
                left join le_subject as sub on c.sub_id = sub.id
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@ALONE_EXAM">
                join ua_student_roll_junior as enroll on sl.enroll_student_id = enroll.id
                join le_alone_exam as ae on sl.alone_exam_id = ae.id
                left join le_course as c on sl.course_id = c.id
                left join le_subject as sub on c.sub_id = sub.id
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@UNIFIED_EXAM">
                JOIN ss_enroll_student_self AS enroll ON sl.enroll_student_id = enroll.id
                <!--                JOIN ss_unified_process_exam AS upe ON sl.unified_process_exam_batch_id = upe.id-->
                join ss_unified_process_exam_batch as upeb on sl.unified_process_exam_batch_id = upeb.id
                LEFT JOIN le_level AS l ON sl.level_id = l.id
            </when>
            <when test="query.type == @com.edu.account.enums.ScoreListTypeEnum@COMPUTER_EXAM">
                join ss_enroll_student_self as enroll on sl.enroll_student_id = enroll.id
                join ss_computer_batch as cb on sl.computer_batch_id = cb.id
                left join le_level as l on sl.level_id = l.id
            </when>
        </choose>
        join le_specialty as s on sl.specialty_id = s.id
        join ua_platform as up on up.id = sl.pl_id
        join ua_school as us on us.id = sl.org_id
        join ua_score_list_details as detail on detail.score_list_id =sl.id
        join le_subject as sub on detail.sub_id = sub.id
        join le_course  as lc on detail.course_id = lc.id
        where
        sl.del = 0
        <if test="query.keyName != null and query.keyName != ''">
            and enroll.student_name like CONCAT('%',#{query.keyName},'%')
        </if>
        <if test="query.plId != null">
            and sl.pl_id = #{query.plId}
        </if>
        <if test="query.orgCode != null">
            and sl.org_code like CONCAT(#{query.orgCode},'%')
        </if>
        <if test="query.gradeId != null">
            and sl.grade_id = #{query.gradeId}
        </if>
        <if test="query.specialtyId != null">
            and sl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.levelId">
            and sl.level_id = #{query.levelId}
        </if>
        <if test="query.phone != null and query.phone != ''">
            and enroll.phone like CONCAT(#{query.phone},'%')
        </if>
        <if test="query.idNumber != null and query.idNumber != ''">
            and enroll.id_number like  CONCAT(#{query.idNumber},'%')
        </if>
        <if test="query.studentRollState != null">
            and enroll.student_roll_state = #{query.studentRollState}
        </if>
        <if test="query.entryState != null">
            and (select IF(IFNULL(SUM(details.exam_score),0) > 0,1,0) from ua_score_list_details as details where details.del = 0 and  details.score_list_id = sl.id) = #{query.entryState}
        </if>
        <if test="query.gradeTerm != null">
            and sl.grade_term_id = #{query.gradeTerm}
        </if>
        <if test="query.unifiedProcessExamBatchId != null">
            and sl.unified_process_exam_batch_id = #{query.unifiedProcessExamBatchId}
        </if>
        <if test="query.computerBatchId != null">
            and sl.computer_batch_id = #{query.computerBatchId}
        </if>
        <if test="query.type != null">
            and sl.type = #{query.type}
        </if>
        <if test="query.isPublish != null">
            and sl.is_publish = #{query.isPublish}
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            and sl.id in
            <foreach collection="query.ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        order by sl.id desc
    </select>


    <resultMap id="PeopleIcon" type="com.edu.account.excel.export.ExamPeopleIconExport">
        <result column="pl_id" property="plId"/>
        <result column="org_code" property="orgCode"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="grade_term" property="gradeTerm"/>
        <result column="specialty_id" property="specialtyId"/>
        <result column="batch_id" property="batchId"/>
        <result column="batch_name" property="batchName"/>
        <result column="ought_number" property="oughtNumber"/>
        <result column="reality_number_sum" property="realityNumber" />
        <result column="course_info_id" property="courseInfo"/>
        <result column="ignore_student_id" property="ignoreStudent"/>
        <result column="course_id" property="courseId"/>
    </resultMap>

    <!--组织条件-->
    <sql id="FinalExamPeopleOrg">
        <if test="query.plId != null">
            and pero.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and pero.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and sp.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and spc.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and sp.plan_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(pero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            pero.exam_end_time is null
            or (DATE_FORMAT(pero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        <if test="query.grade != null and query.grade.size() != 0">
            and
            <foreach collection="query.grade" item="grade" open="(" separator="or" close=")">
                (spc.grade_id = #{grade.gradeId}
                and spc.semester in
                <foreach collection="grade.term" item="term" open="(" separator="," close=")">
                    #{term}
                </foreach>
                )
            </foreach>
        </if>
    </sql>
    <!--课程条件-->
    <sql id="FinalExamPeopleCourse">
        <if test="query.plId != null">
            and aperc.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and aperc.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and sp.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and aperc.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and sp.plan_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(aperc.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            aperc.exam_end_time is null
            or (DATE_FORMAT(aperc.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        <if test="query.grade != null and query.grade.size() != 0">
            and
            <foreach collection="query.grade" item="grade" open="(" separator="or" close=")">
                (aperc.grade_id = #{grade.gradeId}
                and aperc.term in
                <foreach collection="grade.term" item="term" open="(" separator="," close=")">
                    #{term}
                </foreach>
                )
            </foreach>
        </if>
    </sql>

    <sql id="studentTest">
        SELECT DISTINCT
            MAX(id) as id,
            pl_id,
            org_code,
            student_id,
            NAME,
            grade_and_batch_id,
            student_type,
            type,
            type_id,
            course_id,
            rule_id,
            cj_rule_id,
            cj_rule_type
        FROM
            le_student_test
        WHERE
            del = 0
        GROUP BY 	pl_id,
                    org_code,
                    student_id,
                    NAME,
                    grade_and_batch_id,
                    student_type,
                    type,
                    type_id,
                    course_id,
                    rule_id,
                    cj_rule_id,
                    cj_rule_type
    </sql>
    <select id="selectFinalExamPeopleIcon" resultMap="PeopleIcon">
        with student_test AS (
        <include refid="studentTest"/>
        )
        SELECT
        t.*
        ,GROUP_CONCAT(t.course_info SEPARATOR ',')      AS course_info_id
        ,SUM(t.reality_number)                          AS reality_number_sum
        <if test="query.examStartTime != null and query.examEndTime != null">
            ,GROUP_CONCAT(t.ignore_student SEPARATOR ',')   AS ignore_student_id
        </if>
        FROM (
        <if test="query.examType == 1">
            SELECT
            sp.pl_id           AS pl_id
            ,sp.org_code        AS org_code
            ,sp.grade_id        AS grade_id
            ,ug.grade_name      AS grade_name
            ,spc.semester       AS grade_term
            ,sp.specialty_id    AS specialty_id
            ,GROUP_CONCAT(DISTINCT spc.course_id SEPARATOR ',') AS course_info
            ,COUNT(DISTINCT lst.id)                             AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',') AS ignore_student
            </if>
            FROM
            ae_plan_exam_rule_org   AS pero
            JOIN ae_specialty_plan  AS sp ON pero.grade_id = sp.grade_id  AND sp.del = '0'
            JOIN ua_grade           AS ug ON sp.grade_id = ug.id
            JOIN ae_specialty_plan_course   AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester  AND spc.del = '0'
            JOIN le_course AS lc  ON spc.course_id = lc.id
<!--            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 1 AND ero.exam_rule_type = 2-->
<!--            <if test="query.examStartTime != null">-->
<!--                and ( DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or ( ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN student_test AS lst ON pero.id = lst.cj_rule_id AND lst.course_id = lc.id  AND lst.cj_rule_type = 0 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_ZK}'
            LEFT JOIN ae_plan_exam_rule_org_student AS stu  ON stu.rule_id = pero.id AND stu.del = 0
            LEFT JOIN ae_plan_exam_rule_course      AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 1 AND perc.exam_rule_type = 1
            WHERE
            pero.del= 0
            and pero.type = 1
            and perc.id is null
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id           AS pl_id
            ,sp.org_code        AS org_code
            ,sp.grade_id        AS grade_id
            ,ug.grade_name      AS grade_name
            ,aperc.term         AS grade_term
            ,sp.specialty_id    AS specialty_id
            ,GROUP_CONCAT(DISTINCT aperc.course_id SEPARATOR ',')   AS course_info
            ,COUNT(DISTINCT lst.id)                                 AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',')     AS ignore_student
            </if>

            FROM
            ae_plan_exam_rule_course    AS aperc
            JOIN ae_specialty_plan      AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN ua_grade               AS ug ON sp.grade_id = ug.id
            JOIN le_course AS lc  ON aperc.course_id = lc.id
--             LEFT JOIN ae_plan_exam_rule_course  AS ero ON ero.plan_id = sp.id AND ero.del = '0' AND ero.type = 1 AND ero.exam_rule_type = 2
<!--            <if test="query.examStartTime != null">-->
<!--                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or (ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN student_test AS lst ON aperc.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 1 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_ZK}'
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = aperc.id
            WHERE
            aperc.del = 0
            and aperc.type = 1
            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,aperc.term

        </if>
        <if test="query.examType == 2">
            SELECT
            sp.pl_id           AS pl_id
            ,sp.org_code        AS org_code
            ,sp.grade_id        AS grade_id
            ,ug.grade_name      AS grade_name
            ,spc.semester       AS grade_term
            ,sp.specialty_id    AS specialty_id
            ,GROUP_CONCAT(DISTINCT spc.course_id SEPARATOR ',') AS course_info
            ,COUNT(DISTINCT lst.id)                             AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',') AS ignore_student
            </if>
            FROM
            ae_plan_exam_rule_org   AS pero
            JOIN ae_specialty_plan  AS sp ON pero.grade_id = sp.grade_id AND sp.del = '0'
            JOIN ua_grade           AS ug ON sp.grade_id = ug.id
            JOIN ae_specialty_plan_course   AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester AND spc.del = '0'
<!--            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 2 AND ero.exam_rule_type = 2-->
<!--            <if test="query.examStartTime != null">-->
<!--                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or (ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN student_test       AS lst ON pero.id = lst.cj_rule_id AND lst.cj_rule_type = 0 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_BK}'
            LEFT JOIN ae_plan_exam_rule_org_student AS stu  ON stu.rule_id = pero.id AND stu.del = 0
            LEFT JOIN ae_plan_exam_rule_course      AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 2 AND perc.exam_rule_type = 1
            WHERE
            pero.del= 0
            and pero.type = 2
            and perc.id is null
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id           AS pl_id
            ,sp.org_code        AS org_code
            ,sp.grade_id        AS grade_id
            ,ug.grade_name      AS grade_name
            ,aperc.term         AS grade_term
            ,sp.specialty_id    AS specialty_id
            ,GROUP_CONCAT(DISTINCT aperc.course_id SEPARATOR ',')   AS course_info
            ,COUNT(DISTINCT lst.id)                                 AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',')     AS ignore_student
            </if>

            FROM
            ae_plan_exam_rule_course AS aperc
            JOIN ae_specialty_plan   AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN ua_grade            AS ug ON sp.grade_id = ug.id
<!--            LEFT JOIN ae_plan_exam_rule_course  AS ero ON ero.plan_id = sp.id and ero.del = '0' AND ero.type = 2 AND ero.exam_rule_type = 2-->
<!--            <if test="query.examStartTime != null">-->
<!--                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or (ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN student_test           AS lst ON aperc.id = lst.cj_rule_id and lst.cj_rule_type = 1 and lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_BK}'
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = aperc.id
            WHERE
            aperc.del = 0
            and aperc.type = 2
            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,aperc.term
        </if>
        <if test="query.examType == 3">
            SELECT
            sp.pl_id       AS pl_id
            ,sp.org_code    AS org_code
            ,sp.grade_id    AS grade_id
            ,ug.grade_name  AS grade_name
            ,spc.semester   AS grade_term
            ,'' AS course_info
            ,( SELECT COUNT(id) FROM ae_plan_exam_rule_org_student WHERE rule_id = pero.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_org   AS pero
            JOIN ae_specialty_plan  AS sp ON pero.grade_id = sp.grade_id AND sp.del = '0'
            JOIN ua_grade           AS ug ON sp.grade_id = ug.id
            JOIN ae_specialty_plan_course   AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester AND spc.del = '0'
<!--            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 3 AND ero.exam_rule_type = 2-->
<!--            <if test="query.examStartTime != null">-->
<!--                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or (ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN student_test       AS lst ON pero.id = lst.cj_rule_id AND lst.cj_rule_type = 0 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_HK}'
            LEFT JOIN ae_plan_exam_rule_course AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 3 AND perc.exam_rule_type = 1
            WHERE
            pero.del= 0
            and pero.type = 3
            and perc.id is null
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id       AS pl_id
            ,sp.org_code    AS org_code
            ,sp.grade_id    AS grade_id
            ,ug.grade_name  AS grade_name
            ,aperc.term     AS grade_term
            ,'' AS course_info
            ,(SELECT COUNT(id) FROM ae_plan_exam_rule_course_student WHERE rule_id = aperc.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_course AS aperc
            JOIN ae_specialty_plan   AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN ua_grade            AS ug ON sp.grade_id = ug.id
            LEFT JOIN student_test AS lst ON aperc.id = lst.cj_rule_id AND lst.cj_rule_type = 1 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_HK}'
<!--            LEFT JOIN ae_plan_exam_rule_course AS ero ON ero.plan_id = sp.id AND ero.del = '0' AND ero.type = 3 AND ero.exam_rule_type = 2-->
<!--            <if test="query.examStartTime != null">-->
<!--                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or (ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = aperc.id
            WHERE
            aperc.del = 0
            and aperc.type = 3
            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,aperc.term
        </if>
        <if test="query.examType == 4">
            SELECT
            sp.pl_id       AS pl_id
            ,sp.org_code    AS org_code
            ,sp.grade_id    AS grade_id
            ,ug.grade_name  AS grade_name
            ,spc.semester   AS grade_term
            ,'' AS course_info
            ,( SELECT COUNT(id) FROM ae_plan_exam_rule_org_student WHERE rule_id = pero.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_org   AS pero
            JOIN ae_specialty_plan  AS sp ON pero.grade_id = sp.grade_id AND sp.del = '0'
            JOIN ua_grade           AS ug ON sp.grade_id = ug.id
            JOIN ae_specialty_plan_course   AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester AND spc.del = '0'
<!--            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 4 AND ero.exam_rule_type = 2-->
<!--            <if test="query.examStartTime != null">-->
<!--                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or (ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN student_test       AS lst ON pero.id = lst.cj_rule_id AND lst.cj_rule_type = 0 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_CX}'
            LEFT JOIN ae_plan_exam_rule_course AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 4 AND perc.exam_rule_type = 1
            WHERE
            pero.del= 0
            and pero.type = 4
            and perc.id is null
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id       AS pl_id
            ,sp.org_code    AS org_code
            ,sp.grade_id    AS grade_id
            ,ug.grade_name  AS grade_name
            ,aperc.term     AS grade_term
            ,'' AS course_info
            ,(SELECT COUNT(id) FROM ae_plan_exam_rule_course_student WHERE rule_id = aperc.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_course AS aperc
            JOIN ae_specialty_plan   AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN ua_grade            AS ug ON sp.grade_id = ug.id
<!--            LEFT JOIN ae_plan_exam_rule_course AS ero ON ero.plan_id = sp.id and ero.del = '0' AND ero.type = 4 AND ero.exam_rule_type = 2-->
<!--            <if test="query.examStartTime != null">-->
<!--                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')-->
<!--                <if test="query.examEndTime != null">-->
<!--                    or (ero.exam_end_time is not null-->
<!--                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') > DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))-->
<!--                    )-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
            LEFT JOIN student_test          AS lst ON aperc.id = lst.cj_rule_id and lst.cj_rule_type = 1 and lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_CX}'
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = aperc.id
            WHERE
            aperc.del = 0
            and aperc.type = 4
            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,aperc.term
        </if>
        ) as t
        GROUP BY t.pl_id,t.org_code,t.grade_id,t.grade_term
    </select>

    <select id="selectAloneExamPeopleIcon" resultMap="PeopleIcon">
        SELECT
        lae.pl_id       AS pl_id
        ,lae.org_code    AS org_code
        ,lae.id          AS batch_id
        ,lae.exam_name   AS batch_name
        ,lae.course_id   AS course_id
        ,COUNT(DISTINCT laes.id)  AS ought_number
        ,COUNT(DISTINCT lst.id) AS reality_number_sum
        FROM
        le_alone_exam AS lae
        LEFT JOIN le_alone_exam_student AS laes ON lae.id = laes.exam_id AND laes.del = '0'
        LEFT JOIN (<include refid="studentTest"/>) AS lst ON lae.id = lst.type_id AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@INDEPENDENT_EXAM}' and lst.student_id = laes.student_id
        WHERE
        lae.del = '0'
        <if test="query.plId != null">
            and lae.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and lae.org_code = #{query.orgCode}
        </if>
        <if test="query.courseId != null">
            and lae.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and lae.exam_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and lae.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(lae.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            lae.exam_end_time is null
            or (DATE_FORMAT(lae.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        GROUP BY lae.pl_id,lae.org_code,lae.id
        ORDER BY lae.pl_id
    </select>

    <select id="selectUnifiedExamPeopleIcon" resultMap="PeopleIcon">
        SELECT
        upebs.pl_id     AS pl_id
        ,upebs.org_code  AS org_code
        ,upebs.batch_id  AS batch_id
        ,upeb.bacth_name AS batch_name
        ,lst.`name`      AS exam_name
        ,upebs.course_id AS course_id
        ,COUNT(DISTINCT upes.id)  AS ought_number
        ,COUNT(DISTINCT lst.student_id)   AS reality_number_sum
        FROM ss_unified_process_exam_batch_specialty AS upebs
        JOIN ss_unified_process_exam_batch AS upeb ON upebs.batch_id = upeb.id AND upeb.del = '0'
        LEFT JOIN ss_unified_process_exam_student AS upes ON upebs.id = upes.exam_specialty_id AND upes.del = '0'
        LEFT JOIN ss_unified_process_exam AS upe ON upe.batch_id = upeb.id AND upe.del = '0'
        LEFT JOIN (<include refid="studentTest"/>) AS lst ON lst.type_id = upe.id AND upes.student_id = lst.student_id AND lst.type in ('${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY1}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY2}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY3}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY4}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_QZ}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_QM}')
        WHERE
        upebs.del = '0'
        <if test="query.plId != null">
            and upeb.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and upeb.org_code = #{query.orgCode}
        </if>
        <if test="query.courseId != null">
            and upebs.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and upeb.bacth_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and upeb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(upeb.bacth_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            upeb.bacth_end_time is null
            or (DATE_FORMAT(upeb.bacth_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        GROUP BY upebs.pl_id,upebs.org_code,upebs.batch_id
        ORDER BY upebs.pl_id
    </select>

    <select id="selectComputerExamPeopleIcon" resultMap="PeopleIcon">
        SELECT
        cbc.pl_id              AS pl_id
        ,cbc.org_code           AS org_code
        ,cbc.computer_batch_id  AS batch_id
        ,cb.batch_name          AS batch_name
        ,lst.`name`             AS exam_name
        ,cbc.course_id        AS course_id
        ,COUNT( DISTINCT cbs.id ) AS ought_number
        ,COUNT( DISTINCT lst.id ) AS reality_number_sum
        FROM
        ss_computer_batch_course AS cbc
        JOIN ss_computer_batch AS cb ON cbc.computer_batch_id = cb.id AND cb.del = '0'
        JOIN ss_specialty_setting steing ON steing.id = cbc.specialty_setting_id
        LEFT JOIN ss_computer_batch_student AS cbs ON cbc.id = cbs.computer_batch_course_id AND cbs.del = '0'
        LEFT JOIN (<include refid="studentTest"/>) AS lst ON lst.type_id = cbc.id AND cbs.student_id = lst.student_id AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@COMPUTER_EXAM}'
        WHERE
        cbc.del = '0'
        <if test="query.plId != null">
            and cbc.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and cbc.org_code = #{query.orgCode}
        </if>
        <if test="query.courseId != null">
            and cbc.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and cb.batch_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and cb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(cb.exam_start_date,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            cb.exam_end_date is null
            or (DATE_FORMAT(cb.exam_end_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        GROUP BY
        cbc.pl_id,cbc.org_code,cbc.computer_batch_id
        ORDER BY
        cbc.pl_id
    </select>

    <resultMap id="PeopleTable" type="com.edu.account.excel.export.ExamPeopleTableExport">
        <result column="pl_id" property="plId"/>
        <result column="org_code" property="orgCode"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="level_id" property="levelId"/>
        <result column="level_name" property="levelName"/>
        <result column="specialty_name" property="specialtyName"/>
        <result column="course_id" property="courseId"/>
        <result column="course_name" property="courseName"/>
        <result column="batch_id" property="batchId"/>
        <result column="batch_name" property="batchName"/>
        <result column="exam_name" property="examName"/>
        <result column="score_type" property="scoreType"/>
        <result column="grade_term" property="gradeTerm"/>
        <result column="ought_number" property="oughtNumber"/>
        <result column="reality_number_sum" property="realityNumber"/>
        <result column="ignore_student_id" property="ignoreStudent"/>
    </resultMap>
    <select id="selectFinalExamPeopleTable" resultMap="PeopleTable">
        with student_test AS (
        <include refid="studentTest"/>
        )
        SELECT
        t.*
        ,SUM(t.reality_number) AS reality_number_sum
        <if test="query.examStartTime != null and query.examEndTime != null">
            ,GROUP_CONCAT(t.ignore_student SEPARATOR ',') AS ignore_student_id
        </if>
        FROM(
        <if test="query.examType == 1">
            SELECT
            sp.pl_id        AS pl_id
            ,sp.org_code     AS org_code
            ,sp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,sp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,sp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,spc.course_id   AS course_id
            ,lc.`name`       AS course_name
            ,sp.id           AS batch_id
            ,sp.plan_name    AS batch_name
            ,lst.`name`      AS exam_name
            ,'正考'           AS score_type
            ,spc.semester    AS grade_term
            ,COUNT( DISTINCT lst.id ) AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',') AS ignore_student
            </if>
            FROM
            ae_plan_exam_rule_org AS pero
            JOIN ae_specialty_plan AS sp  ON pero.grade_id = sp.grade_id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN ae_specialty_plan_course AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester AND spc.del = '0'
            JOIN le_course AS lc  ON spc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 1 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test AS lst ON pero.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 0 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_ZK}'
            LEFT JOIN ae_plan_exam_rule_org_student AS stu  ON stu.rule_id = ero.id AND stu.del = 0
            LEFT JOIN ae_plan_exam_rule_course AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 1 AND perc.exam_rule_type = 1
            WHERE
            pero.del = 0
            AND pero.type = 1
            AND perc.id IS NULL
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id,sp.specialty_id,lc.id,sp.id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id        AS pl_id
            ,sp.org_code     AS org_code
            ,sp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,sp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,sp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,aperc.course_id AS course_id
            ,lc.`name`       AS course_name
            ,sp.id           AS batch_id
            ,sp.plan_name    AS batch_name
            ,lst.`name` AS exam_name
            ,'正考'           AS score_type
            ,aperc.term      AS grade_term
            ,COUNT( DISTINCT lst.id ) AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',') AS ignore_student
            </if>
            FROM
            ae_plan_exam_rule_course AS aperc
            JOIN ae_specialty_plan AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN le_course AS lc ON aperc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_course AS ero ON ero.plan_id = sp.id AND ero.del = '0' AND ero.type = 1 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test          AS lst ON aperc.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 1 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_ZK}'
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = ero.id
            WHERE
            aperc.del = 0
            AND aperc.type = 1
            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id,sp.specialty_id,lc.id,sp.id,aperc.term
        </if>
        <if test="query.examType == 2">
            SELECT
            sp.pl_id        AS pl_id
            ,sp.org_code     AS org_code
            ,sp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,sp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,sp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,spc.course_id   AS course_id
            ,lc.`name`       AS course_name
            ,sp.id           AS batch_id
            ,sp.plan_name    AS batch_name
            ,lst.`name`      AS exam_name
            ,'补考'           AS score_type
            ,spc.semester    AS grade_term
            ,COUNT( DISTINCT lst.id ) AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',') AS ignore_student
            </if>
            FROM
            ae_plan_exam_rule_org AS pero
            JOIN ae_specialty_plan AS sp  ON pero.grade_id = sp.grade_id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN ae_specialty_plan_course AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester AND spc.del = '0'
            JOIN le_course AS lc ON spc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 2 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (
                DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (
                    ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test AS lst ON pero.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 0 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_BK}'
            LEFT JOIN ae_plan_exam_rule_org_student AS stu  ON stu.rule_id = ero.id AND stu.del = 0
            LEFT JOIN ae_plan_exam_rule_course AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 2 AND perc.exam_rule_type = 1
            WHERE
            pero.del= 0
            and pero.type = 2
            and perc.id is null
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id,sp.specialty_id,lc.id,sp.id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id                 AS pl_id,
            sp.org_code              AS org_code,
            sp.grade_id              AS grade_id,
            ug.grade_name            AS grade_name,
            sp.level_id              AS level_id,
            ll.level_name            AS level_name,
            sp.specialty_id          AS specialty_id,
            ls.spe_name              AS specialty_name,
            aperc.course_id          AS course_id,
            lc.`name`                AS course_name,
            sp.id                    AS batch_id,
            sp.plan_name             AS batch_name,
            lst.`name`              AS exam_name,
            '补考'                    AS score_type,
            aperc.term               AS grade_term,
            COUNT( DISTINCT lst.id ) AS reality_number
            <if test="query.examStartTime != null and query.examEndTime != null">
                ,GROUP_CONCAT(DISTINCT stu.enroll_id SEPARATOR ',') AS ignore_student
            </if>
            FROM
            ae_plan_exam_rule_course AS aperc
            JOIN ae_specialty_plan AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN le_course AS lc ON aperc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_course AS ero ON ero.plan_id = sp.id AND ero.del = '0' AND ero.type = 2 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test AS lst ON aperc.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 1 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_BK}'
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = ero.id
            WHERE
            aperc.del = 0
            and aperc.type = 2
            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id,sp.specialty_id,lc.id,sp.id,aperc.term
        </if>
        <if test="query.examType == 3">
            SELECT
            sp.pl_id        AS pl_id
            ,sp.org_code     AS org_code
            ,sp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,sp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,sp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,spc.course_id   AS course_id
            ,lc.`name`       AS course_name
            ,sp.id           AS batch_id
            ,sp.plan_name    AS batch_name
            ,lst.`name`      AS exam_name
            ,'缓考'           AS score_type
            ,spc.semester    AS grade_term
            ,(SELECT COUNT(id) FROM ae_plan_exam_rule_org_student WHERE rule_id = pero.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_org AS pero
            JOIN ae_specialty_plan AS sp ON pero.grade_id = sp.grade_id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN ae_specialty_plan_course AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester AND spc.del = '0'
            JOIN le_course AS lc ON spc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 3 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test AS lst ON pero.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 0 and lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_HK}'
            LEFT JOIN ae_plan_exam_rule_course AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 3 AND perc.exam_rule_type = 1
            WHERE
            pero.del= 0
            and pero.type = 3
            and perc.id is null
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id,sp.specialty_id,lc.id,sp.id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id        AS pl_id
            ,sp.org_code     AS org_code
            ,sp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,sp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,sp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,aperc.course_id AS course_id
            ,lc.`name`       AS course_name
            ,sp.id           AS batch_id
            ,sp.plan_name    AS batch_name
            ,lst.`name`      AS exam_name
            ,'缓考'           AS score_type
            ,aperc.term      AS grade_term
            ,(SELECT COUNT(a.id) FROM ae_plan_exam_rule_course_student as a WHERE rule_id = aperc.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_course AS aperc
            JOIN ae_specialty_plan  AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN le_course AS lc ON aperc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_course AS ero ON ero.plan_id = sp.id AND ero.del = '0' AND ero.type = 3 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test AS lst ON aperc.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 1 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_HK}'
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = ero.id
            WHERE
            aperc.del = 0
            and aperc.type = 3

            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id, sp.specialty_id,lc.id,sp.id,aperc.term
        </if>
        <if test="query.examType == 4">
            SELECT
            sp.pl_id        AS pl_id
            ,sp.org_code     AS org_code
            ,sp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,sp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,sp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,spc.course_id   AS course_id
            ,lc.`name`       AS course_name
            ,sp.id           AS batch_id
            ,sp.plan_name    AS batch_name
            ,lst.`name`      AS exam_name
            ,'重修'           AS score_type
            ,spc.semester    AS grade_term
            ,( SELECT COUNT(id) FROM ae_plan_exam_rule_org_student WHERE rule_id = pero.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_org AS pero
            JOIN ae_specialty_plan AS sp ON pero.grade_id = sp.grade_id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN ae_specialty_plan_course AS spc ON sp.id = spc.plan_id AND pero.term = spc.semester AND spc.del = '0'
            JOIN le_course AS lc ON spc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_org AS ero ON ero.grade_id = sp.grade_id AND ero.del = 0 AND ero.type = 4 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test AS lst ON pero.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 0 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_CX}'
            LEFT JOIN ae_plan_exam_rule_course AS perc ON perc.grade_id = sp.grade_id AND perc.del = '0' AND perc.type = 4 AND perc.exam_rule_type = 1
            WHERE
            pero.del= 0
            and pero.type = 4
            and perc.id is null
            <include refid="FinalExamPeopleOrg" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id,sp.specialty_id,lc.id,sp.id,spc.semester

            UNION ALL

            SELECT
            sp.pl_id        AS pl_id
            ,sp.org_code     AS org_code
            ,sp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,sp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,sp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,aperc.course_id AS course_id
            ,lc.`name`       AS course_name
            ,sp.id           AS batch_id
            ,sp.plan_name    AS batch_name
            ,lst.`name`      AS exam_name
            ,'重修'           AS score_type
            ,aperc.term      AS grade_term
            ,(SELECT COUNT(a.id) FROM ae_plan_exam_rule_course_student as a WHERE rule_id = aperc.id AND del = 0 ) AS ought_number
            ,COUNT(DISTINCT lst.id) AS reality_number
            FROM
            ae_plan_exam_rule_course AS aperc
            JOIN ae_specialty_plan AS sp ON aperc.plan_id = sp.id AND sp.del = '0'
            JOIN le_level AS ll ON sp.level_id = ll.id
            JOIN ua_grade AS ug ON sp.grade_id = ug.id
            JOIN le_specialty AS ls ON sp.specialty_id = ls.id
            JOIN le_course AS lc ON aperc.course_id = lc.id
            LEFT JOIN ae_plan_exam_rule_course AS ero ON ero.plan_id = sp.id AND ero.del = '0' AND ero.type = 4 AND ero.exam_rule_type = 2
            <if test="query.examStartTime != null">
                and (DATE_FORMAT(ero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
                <if test="query.examEndTime != null">
                    or (ero.exam_end_time is not null
                    and (DATE_FORMAT(ero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
                    )
                </if>
                )
            </if>
            LEFT JOIN student_test AS lst ON aperc.id = lst.cj_rule_id AND lst.course_id = lc.id AND lst.cj_rule_type = 1 AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@FINAL_EXAM_CX}'
            LEFT JOIN ae_plan_exam_rule_course_student AS stu ON stu.rule_id = ero.id
            WHERE
            aperc.del = 0
            and aperc.type = 4
            <include refid="FinalExamPeopleCourse" />
            GROUP BY sp.pl_id,sp.org_code,sp.grade_id,sp.level_id,sp.specialty_id,lc.id,sp.id,aperc.term
        </if>
        ) as t
        GROUP BY t.pl_id,t.org_code,t.grade_id,t.level_id,t.specialty_id,t.course_id,t.batch_id,t.grade_term

    </select>

    <select id="selectAloneExamPeopleTable" resultMap="PeopleTable">
        SELECT
        lae.pl_id         AS pl_id
        ,lae.org_code      AS org_code
        ,lae.id            AS batch_id
        ,lae.exam_name     AS batch_name
        ,lst.`name`        AS exam_name
        ,lae.course_id     AS course_id
        ,lc.`name`         AS course_name
        ,laes.specialty_id AS specialty_id
        ,ls.spe_name       AS specialty_name
        ,'独立考试'           AS score_type
        ,COUNT( DISTINCT laes.id )  AS ought_number
        ,COUNT( DISTINCT lst.id ) AS reality_number_sum
        FROM
        le_alone_exam AS lae
        LEFT JOIN (
        SELECT
        laes.*,
        IFNULL( aes.specialty_id, IFNULL( ses.specialty_id, usrj.specialty_id ) ) AS specialty_id
        FROM
        le_alone_exam_student AS laes
        LEFT JOIN ae_enroll_student_adult AS aes ON aes.student_id = laes.student_id AND laes.enroll_student_id = aes.id AND aes.del = '0'
        LEFT JOIN ss_enroll_student_self AS ses ON ses.student_id = laes.student_id AND laes.enroll_student_id = ses.id AND ses.del = '0'
        LEFT JOIN ua_student_roll_junior AS usrj ON usrj.student_id = laes.student_id AND usrj.id = laes.enroll_student_id AND usrj.del = '0'
        WHERE
        laes.del = '0'
        ) AS laes ON lae.id = laes.exam_id
        LEFT JOIN le_specialty AS ls ON laes.specialty_id = ls.id
        LEFT JOIN le_course AS lc ON lae.course_id = lc.id
        LEFT JOIN (<include refid="studentTest"/>) AS lst ON lae.id = lst.type_id AND lst.type = '${@com.edu.lessons.enums.StudentTestTypeEnum@INDEPENDENT_EXAM}' AND lst.student_id = laes.student_id
        WHERE
        lae.del = '0'
        <if test="query.plId != null">
            and lae.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and lae.org_code = #{query.orgCode}
        </if>
        <if test="query.courseId != null">
            and lae.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and lae.exam_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and lae.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(lae.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            lae.exam_end_time is null
            or (DATE_FORMAT(lae.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        GROUP BY lae.pl_id,lae.org_code,lae.id,lae.course_id,laes.specialty_id
        ORDER BY lae.pl_id
    </select>

    <select id="selectUnifiedExamPeopleTable" resultMap="PeopleTable">
        SELECT
        upebs.pl_id      AS pl_id
        ,upebs.org_code   AS org_code
        ,upebs.batch_id   AS batch_id
        ,upeb.bacth_name  AS batch_name
        ,lst.`name`      AS exam_name
        ,upebs.course_id  AS course_id
        ,lc.`name`        AS course_name
        ,upebs.specialty_id AS specialty_id
        ,ls.spe_name        AS specialty_name
        ,'统考过程性'           AS score_type
        ,COUNT(DISTINCT upes.id)     AS ought_number
        ,COUNT(DISTINCT lst.student_id) AS reality_number_sum
        FROM ss_unified_process_exam_batch_specialty AS upebs
        JOIN le_specialty AS ls ON upebs.specialty_id = ls.id
        JOIN le_course AS lc ON upebs.course_id = lc.id
        JOIN ss_unified_process_exam_batch AS upeb ON upebs.batch_id = upeb.id AND upeb.del = '0'
        LEFT JOIN ss_unified_process_exam_student AS upes ON upebs.id = upes.exam_specialty_id AND upes.del = '0'
        LEFT JOIN ss_unified_process_exam AS upe ON upe.batch_id = upeb.id AND upe.del = '0'
        LEFT JOIN (<include refid="studentTest"/>) AS lst ON lst.type_id = upe.id AND upes.student_id = lst.student_id AND lst.type in ('${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY1}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY2}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY3}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_ZY4}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_QZ}','${@com.edu.lessons.enums.StudentTestTypeEnum@UNX_EXAM_QM}')
        WHERE
        upebs.del = '0'
        <if test="query.plId != null">
            and upeb.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and upeb.org_code = #{query.orgCode}
        </if>
        <if test="query.courseId != null">
            and upebs.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and upeb.bacth_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and upeb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(upeb.bacth_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            upeb.bacth_end_time is null
            or (DATE_FORMAT(upeb.bacth_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        GROUP BY upebs.pl_id,upebs.org_code,upebs.batch_id,upebs.specialty_id,upebs.course_id
    </select>

    <select id="selectComputerExamPeopleTable" resultMap="PeopleTable">
        SELECT
        cbc.pl_id                AS pl_id
        ,cbc.org_code             AS org_code
        ,cbc.computer_batch_id    AS batch_id
        ,cb.batch_name            AS batch_name
        ,lst.`name`               AS exam_name
        ,cbc.course_id            AS course_id
        ,lc.`name`                AS course_name
        ,steing.specialty_id      AS specialty_id
        ,ls.spe_name              AS specialty_name
        ,'机考'           AS score_type
        ,COUNT( DISTINCT cbs.id ) AS ought_number
        ,COUNT( DISTINCT lst.id ) AS reality_number_sum
        FROM
        ss_computer_batch_course AS cbc
        JOIN ss_specialty_setting steing ON steing.id = cbc.specialty_setting_id
        JOIN le_specialty AS ls ON steing.specialty_id = ls.id
        JOIN le_course AS lc ON cbc.course_id = lc.id
        JOIN ss_computer_batch AS cb ON cbc.computer_batch_id = cb.id AND cb.del = '0'
        LEFT JOIN ss_computer_batch_student AS cbs ON cbc.id = cbs.computer_batch_course_id AND cbs.del = '0'
        LEFT JOIN (<include refid="studentTest"/>) AS lst ON lst.type_id = cbc.id AND cbs.student_id = lst.student_id AND lst.type = 'COMPUTER_EXAM'
        WHERE
        cbc.del = '0'
        <if test="query.plId != null">
            and cbc.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and cbc.org_code = #{query.orgCode}
        </if>
        <if test="query.courseId != null">
            and cbc.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and cb.batch_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and cb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(cb.exam_start_date,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            cb.exam_end_date is null
            or (DATE_FORMAT(cb.exam_end_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        GROUP BY
        cbc.pl_id,cbc.org_code,cbc.computer_batch_id,steing.specialty_id,cbc.course_id
        ORDER BY
        cbc.pl_id
    </select>

    <resultMap id="StudentCondition" type="com.edu.account.model.dto.StudentConditionDTO">
        <result column="student_id" property="studentId"/>
        <result column="course_rate" property="courseRate"/>
        <result column="zy_count" property="zyCount"/>
        <result column="lx_count" property="lxCount"/>
    </resultMap>
    <select id="getFinalCollectivityStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        aesa.student_id         AS student_id
        ,lfec.study_schedule     AS course_rate
        ,lfec.work_schedule      AS zy_count
        ,lfec.practice_schedule  AS lx_count
        FROM le_final_exam_condition AS lfec
        JOIN ae_enroll_student_adult AS aesa ON lfec.pl_id = aesa.pl_id AND lfec.org_code = aesa.one_org_code AND aesa.del = '0'
        WHERE
        lfec.del = '0'
        AND lfec.parent_id = 0
        AND lfec.`status` = 0
        AND lfec.pl_id = #{plId}
        AND lfec.org_code = #{orgCode}
    </select>

    <select id="getFinalPersonageStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        lfecs.student_id        AS student_id
        ,lfec.study_schedule     AS course_rate
        ,lfec.work_schedule      AS zy_count
        ,lfec.practice_schedule  AS lx_count
        FROM le_final_exam_condition AS lfec
        JOIN le_final_exam_condition_student AS lfecs ON lfec.id = lfecs.rule_id AND lfecs.del = '0'
        WHERE
        lfec.del = '0'
        AND lfec.parent_id != 0
        AND lfec.`status` = 0
        AND lfec.pl_id = #{plId}
        AND lfec.org_code = #{orgCode}
    </select>

    <select id="getAloneCollectivityStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        laes.student_id         AS student_id
        ,laec.study_schedule     AS course_rate
        ,laec.work_schedule      AS zy_count
        ,laec.practice_schedule  AS lx_count
        FROM le_alone_exam_condition AS laec
        JOIN le_alone_exam AS lae ON laec.pl_id = lae.pl_id AND laec.org_code = lae.org_code AND lae.del = '0'
        JOIN le_alone_exam_student AS laes ON lae.id = laes.exam_id AND laes.del = '0'
        WHERE
        laec.del = '0'
        AND laec.parent_id = 0
        AND laec.`status` = 0
        AND laec.pl_id = #{plId}
        AND laec.org_code = #{orgCode}
        AND lae.id = #{batchId}
    </select>


    <select id="getAlonePersonageStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        laes.student_id         AS student_id
        ,laec.study_schedule     AS course_rate
        ,laec.work_schedule      AS zy_count
        ,laec.practice_schedule  AS lx_count
        FROM le_alone_exam_condition AS laec
        JOIN le_alone_exam AS lae ON laec.pl_id = lae.pl_id AND laec.org_code = lae.org_code AND lae.del = '0'
        JOIN le_alone_exam_student AS laes ON lae.id = laes.exam_id AND laes.del = '0'
        JOIN le_alone_exam_condition_student AS laecs ON laec.id = laecs.condition_id AND laes.student_id = laecs.student_id AND laecs.del = '0'
        WHERE
        laec.del = '0'
        AND laec.parent_id != 0
        AND laec.`status` = 0
        AND laec.pl_id = #{plId}
        AND laec.org_code = #{orgCode}
        AND lae.id = #{batchId}
    </select>

    <select id="getUnifiedCollectivityStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        upes.student_id         AS student_id
        ,suec.study_schedule     AS course_rate
        ,suec.work_schedule      AS zy_count
        ,suec.practice_schedule  AS lx_count
        FROM ss_unified_exam_condition AS suec
        JOIN ss_unified_process_exam_batch_specialty AS upebs ON suec.pl_id = upebs.pl_id AND suec.org_code = upebs.org_code AND upebs.del = '0'
        JOIN ss_unified_process_exam_student AS upes ON upebs.id = upes.exam_specialty_id AND upes.del = '0'
        WHERE
        suec.del = '0'
        AND suec.parent_id = 0
        AND suec.`status` = 0
        AND suec.pl_id = #{plId}
        AND suec.org_code = #{orgCode}
        AND upebs.id = #{batchId}
    </select>

    <select id="getUnifiedPersonageStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        upes.student_id         AS student_id
        ,suec.study_schedule     AS course_rate
        ,suec.work_schedule      AS zy_count
        ,suec.practice_schedule  AS lx_count
        FROM ss_unified_exam_condition AS suec
        JOIN ss_unified_process_exam_batch_specialty AS upebs ON suec.pl_id = upebs.pl_id AND suec.org_code = upebs.org_code AND upebs.del = '0'
        JOIN ss_unified_process_exam_student AS upes ON upebs.id = upes.exam_specialty_id AND upes.del = '0'
        JOIN ss_unified_exam_condition_student AS suecs ON suec.id = suecs.rule_id AND upes.student_id = suecs.student_id AND suecs.del = '0'
        WHERE
        suec.del = '0'
        AND suec.parent_id != 0
        AND suec.`status` = 0
        AND suec.pl_id = #{plId}
        AND suec.org_code = #{orgCode}
        AND upebs.id = #{batchId}
    </select>

    <select id="getComputerCollectivityStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        cbs.student_id         AS student_id
        ,scbc.study_plan        AS course_rate
        ,scbc.job_plan          AS zy_count
        ,scbc.exercise_plan     AS lx_count
        FROM ss_computer_batch_condition AS scbc
        JOIN ss_computer_batch_course AS cbc ON scbc.pl_id = cbc.pl_id AND scbc.org_code = cbc.org_code AND cbc.del = '0'
        JOIN ss_computer_batch_student AS cbs ON cbc.id = cbs.computer_batch_course_id AND cbs.del = '0'
        WHERE
        scbc.del = '0'
        AND scbc.parent_id = 0
        AND scbc.`state` = 0
        AND scbc.pl_id = #{plId}
        AND scbc.org_code = #{orgCode}
        AND cbc.computer_batch_id = #{batchId}
    </select>

    <select id="getComputerPersonageStudent" resultMap="StudentCondition">
        SELECT
        DISTINCT
        cbs.student_id         AS student_id
        ,scbc.study_plan        AS course_rate
        ,scbc.job_plan          AS zy_count
        ,scbc.exercise_plan     AS lx_count
        FROM ss_computer_batch_condition AS scbc
        JOIN ss_computer_batch_course AS cbc ON scbc.pl_id = cbc.pl_id AND scbc.org_code = cbc.org_code AND cbc.del = '0'
        JOIN ss_computer_batch_student AS cbs ON cbc.id = cbs.computer_batch_course_id AND cbs.del = '0'
        JOIN ss_computer_batch_condition_student AS scbcs ON scbc.id = scbcs.batch_condition_id AND cbs.student_id = scbcs.student_id AND scbcs.del = '0'
        WHERE
        scbc.del = '0'
        AND scbc.parent_id != 0
        AND scbc.`state` = 0
        AND scbc.pl_id = #{plId}
        AND scbc.org_code = #{orgCode}
        AND cbc.computer_batch_id = #{batchId}
    </select>

    <resultMap id="ExamScoreIconExport" type="com.edu.account.excel.export.ExamScoreIconExport">
        <result column="pl_id" property="plId"/>
        <result column="org_code" property="orgCode"/>
        <result column="grade_name" property="gradeName"/>
        <result column="grade_term" property="gradeTerm"/>
        <result column="unified_process_name" property="unifiedProcessName"/>
        <result column="computer_batch_name" property="computerBatchName"/>
        <result column="total_student_num" property="totalStudentNum"/>
        <result column="pass_num" property="passNum"/>
        <result column="nine_num" property="nineNum"/>
        <result column="sixty_num" property="sixtyNum"/>
        <result column="no_pass_num" property="noPassNum"/>
    </resultMap>

    <select id="selectFinalExamScoreIcon" resultMap="ExamScoreIconExport">
        SELECT
        usl.pl_id
        ,usl.org_code
        ,ug.grade_name
        ,usl.grade_term_id as grade_term
        ,count(usld.id) as total_student_num
        ,SUM(IF(usld.composite_score >= 60,1,0)) as pass_num
        ,SUM(IF(usld.composite_score >= 90,1,0)) as nine_num
        ,SUM(IF(usld.composite_score >= 60 and usld.composite_score &lt; 90,1,0)) as sixty_num
        ,SUM(IF(usld.composite_score is null or usld.composite_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list as usl
        JOIN ua_score_list_details as usld on usl.id = usld.score_list_id
        JOIN ua_grade as ug on usl.grade_id = ug.id
--         JOIN ae_specialty_plan as asp on asp.pl_id = usl.pl_id and asp.specialty_id = usl.specialty_id and asp.grade_id = usl.grade_id and asp.level_id = usl.level_id
--         JOIN ae_specialty_plan_course as aspc on aspc.plan_id = asp.id and aspc.course_id = usld.course_id
        LEFT JOIN le_student_test AS lst ON usl.student_test_Id = lst.id
        WHERE usl.del = 0
        and usl.type = #{query.type}
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.scoreType != null and query.scoreType != ''">
            and usld.score_type = #{query.scoreType}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usld.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
--             and asp.plan_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        <if test="query.grade != null and query.grade.size() != 0">
            and
            <foreach collection="query.grade" item="grade" open="(" separator="or" close=")">
                (usl.grade_id = #{grade.gradeId}
                and usld.grade_term in
                <foreach collection="grade.term" item="term" open="(" separator="," close=")">
                    #{term}
                </foreach>
                )
            </foreach>
        </if>
        GROUP BY usl.pl_id,usl.org_code,usl.grade_id,usl.grade_term_id
        order by usl.pl_id asc,usl.grade_id asc,usl.grade_term_id asc
    </select>

    <select id="selectAloneExamScoreIcon" resultMap="ExamScoreIconExport">
        SELECT
        usl.pl_id
        ,usl.org_code
        ,count(usl.id) as total_student_num
        ,SUM(IF(usl.exam_score >= 60,1,0)) as pass_num
        ,SUM(IF(usl.exam_score >= 90,1,0)) as nine_num
        ,SUM(IF(usl.exam_score >= 60 and usl.exam_score &lt; 90,1,0)) as sixty_num
        ,SUM(IF(usl.exam_score is null or usl.exam_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list as usl
        join le_alone_exam as lae on usl.alone_exam_id = lae.id
        LEFT JOIN le_student_test as lst ON usl.student_test_Id = lst.id
        WHERE usl.del = 0
        and usl.type = #{query.type}
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usl.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and lae.exam_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and lae.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usl.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usl.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        GROUP BY usl.pl_id,usl.org_code
        order by usl.pl_id asc
    </select>

    <select id="selectUnifiedExamScoreIcon" resultMap="ExamScoreIconExport">
        SELECT
        usl.pl_id
        ,usl.org_code
        ,usl.unified_process_exam_batch_id
        ,supeb.bacth_name as unified_process_name
        ,count(usld.id) as total_student_num
        ,SUM(IF(usld.unified_score >= 60,1,0)) as pass_num
        ,SUM(IF(usld.unified_score >= 90,1,0)) as nine_num
        ,SUM(IF(usld.unified_score >= 60 and usld.unified_score &lt; 90,1,0)) as sixty_num
        ,SUM(IF(usld.unified_score is null or usld.unified_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list as usl
        JOIN ua_score_list_details as usld on usl.id = usld.score_list_id
        JOIN ss_unified_process_exam_batch as supeb on usl.unified_process_exam_batch_id = supeb.id
        LEFT JOIN le_student_test as lst ON usl.student_test_Id = lst.id
        WHERE
        usl.del = 0
        and usl.type = #{query.type}
        and usld.unified_score is not null
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usld.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and supeb.bacth_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and supeb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        GROUP BY usl.pl_id,usl.org_code,usl.unified_process_exam_batch_id
        order by usl.pl_id asc,usl.unified_process_exam_batch_id asc
    </select>

    <select id="selectComputerExamScoreIcon" resultMap="ExamScoreIconExport">
        SELECT
        usl.pl_id
        ,usl.org_code
        ,usl.computer_batch_id
        ,scb.batch_name as computer_batch_name
        ,count(usld.id) as total_student_num
        ,SUM(IF(usld.composite_score >= 60,1,0)) as pass_num
        ,SUM(IF(usld.composite_score >= 90,1,0)) as nine_num
        ,SUM(IF(usld.composite_score >= 60 and usld.unified_score &lt; 90,1,0)) as sixty_num
        ,SUM(IF(usld.composite_score is null or usld.composite_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list as usl
        JOIN ua_score_list_details as usld on usl.id = usld.score_list_id
        JOIN ss_computer_batch as scb on usl.computer_batch_id = scb.id
        LEFT JOIN le_student_test as lst ON usl.student_test_Id = lst.id
        WHERE usl.del = 0
        and usl.type = #{query.type}
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usld.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and scb.batch_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and scb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        GROUP BY usl.pl_id,usl.org_code,usl.computer_batch_id
        order by usl.pl_id asc,usl.computer_batch_id asc
    </select>

    <resultMap id="ExamScoreTableExport" type="com.edu.account.excel.export.ExamScoreTableExport">
        <result column="pl_id" property="plId"/>
        <result column="org_code" property="orgCode"/>
        <result column="grade_name" property="gradeName"/>
        <result column="level_name" property="levelName"/>
        <result column="specialty_name" property="specialtyName"/>
        <result column="course_name" property="courseName"/>
        <result column="bath_name" property="bathName"/>
        <result column="exam_name" property="examName"/>
        <result column="exae_type" property="exaeType"/>
        <result column="grade_term" property="gradeTerm"/>
        <result column="exam_end_time" property="examEndTime"/>
        <result column="total_student_num" property="totalStudentNum"/>
        <result column="nine_num" property="nineNum"/>
        <result column="eight_num" property="eightNum"/>
        <result column="seven_num" property="sevenNum"/>
        <result column="six_num" property="sixNum"/>
        <result column="less_six_num" property="lessSixNum"/>
        <result column="pass_num" property="passNum"/>
        <result column="no_pass_num" property="noPassNum"/>
    </resultMap>

    <select id="selectFinalExamScoreTable" resultMap="ExamScoreTableExport">
        SELECT
        usl.pl_id as pl_id
        ,usl.org_code as org_code
        ,ug.grade_name as grade_name
        ,ll.level_name as level_name
        ,ls.spe_name as specialty_name
        ,lc.name as course_name
        ,usld.score_type as exae_type
        ,usld.grade_term as grade_term
--         ,asp.plan_name as bath_name
        ,MAX(usld.create_time) as exam_end_time
        ,lst.`name`      as exam_name
        ,count(usld.id) as total_student_num
        ,SUM(IF(usld.composite_score >= 90,1,0)) as nine_num
        ,SUM(IF(usld.composite_score >= 80 and usld.composite_score &lt;= 90,1,0)) as eight_num
        ,SUM(IF(usld.composite_score >= 70 and usld.composite_score &lt;= 80,1,0)) as seven_num
        ,SUM(IF(usld.composite_score >= 60 and usld.composite_score &lt;= 70,1,0)) as six_num
        ,SUM(IF(usld.composite_score is null or usld.composite_score &lt;= 60,1,0))  as less_six_num
        ,SUM(IF(usld.composite_score >= 60,1,0)) as pass_num
        ,SUM(IF(usld.composite_score is null or usld.composite_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list_details as usld
        JOIN ua_score_list as usl on usl.id = usld.score_list_id
        JOIN ua_grade as ug on ug.id = usl.grade_id
        JOIN le_specialty as ls on ls.id = usl.specialty_id
        JOIN le_level as ll on ll.id = usl.level_id
        JOIN le_course as lc on lc.id = usld.course_id
--         JOIN ae_specialty_plan as asp on asp.pl_id = usl.pl_id and asp.specialty_id = usl.specialty_id and asp.grade_id = usl.grade_id and asp.level_id = usl.level_id
--         JOIN ae_specialty_plan_course as aspc on aspc.plan_id = asp.id and aspc.course_id = usld.course_id
        LEFT JOIN le_student_test as lst ON usl.student_test_Id = lst.id
        WHERE usl.del = 0
        and usl.type = #{query.type}
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.scoreType != null and query.scoreType != ''">
            and usld.score_type = #{query.scoreType}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usld.course_id = #{query.courseId}
        </if>
        <if test="query.examIds != null and query.batchName != ''">
--             and asp.plan_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        <if test="query.grade != null and query.grade.size() != 0">
            and
            <foreach collection="query.grade" item="grade" open="(" separator="or" close=")">
                (usl.grade_id = #{grade.gradeId}
                and usld.grade_term in
                <foreach collection="grade.term" item="term" open="(" separator="," close=")">
                    #{term}
                </foreach>
                )
            </foreach>
        </if>
        GROUP BY usl.pl_id,usl.org_code,usl.grade_id,usl.specialty_id,usld.course_id,usld.score_type, usl.grade_term_id
        order by usl.pl_id asc,usl.grade_id asc,usl.grade_term_id asc
    </select>

    <select id="selectAloneExamScoreTable" resultMap="ExamScoreTableExport">
        SELECT
        usl.pl_id
        ,usl.org_code
        ,ls.spe_name as specialty_name
        ,lc.name as course_name
        ,lae.exam_name as bath_name
        ,lst.`name`      as exam_name
        ,lae.exam_end_time as exam_end_time
        ,'独立考试' as exaeType
        ,count(usl.id) as total_student_num
        ,SUM(IF(usl.exam_score >= 90,1,0)) as nine_num
        ,SUM(IF(usl.exam_score >= 80 and usl.exam_score &lt;= 90,1,0)) as eight_num
        ,SUM(IF(usl.exam_score >= 70 and usl.exam_score &lt;= 80,1,0)) as seven_num
        ,SUM(IF(usl.exam_score >= 60 and usl.exam_score &lt;= 70,1,0)) as six_num
        ,SUM(IF(usl.exam_score is null or usl.exam_score &lt;= 60,1,0))  as less_six_num
        ,SUM(IF(usl.exam_score >= 60,1,0)) as pass_num
        ,SUM(IF(usl.exam_score is null or usl.exam_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list as usl
        join le_alone_exam as lae on usl.alone_exam_id = lae.id
        JOIN le_specialty as ls on ls.id = usl.specialty_id
        JOIN le_course as lc on lc.id = usl.course_id
        LEFT JOIN le_student_test as lst ON usl.student_test_Id = lst.id
        WHERE usl.del = 0
        and usl.type = #{query.type}
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usl.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and lae.exam_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and lae.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usl.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usl.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        GROUP BY usl.pl_id,usl.org_code,usl.specialty_id,usl.course_id
        order by usl.pl_id asc
    </select>

    <select id="selectUnifiedExamScoreTable" resultMap="ExamScoreTableExport">
        SELECT
        usl.pl_id
        ,usl.org_code
        ,ls.spe_name as specialty_name
        ,lc.name as course_name
        ,supeb.bacth_name as bath_name
        ,lst.`name`      as exam_name
        ,supeb.bacth_end_time as exam_end_time
        ,'统考过程性' as exaeType
        ,count(usld.id) as total_student_num
        ,SUM(IF(usld.unified_score >= 90,1,0)) as nine_num
        ,SUM(IF(usld.unified_score >= 80 and usld.unified_score &lt;= 90,1,0)) as eight_num
        ,SUM(IF(usld.unified_score >= 70 and usld.unified_score &lt;= 80,1,0)) as seven_num
        ,SUM(IF(usld.unified_score >= 60 and usld.unified_score &lt;= 70,1,0)) as six_num
        ,SUM(IF(usld.unified_score is null or usld.unified_score &lt;= 60,1,0))  as less_six_num
        ,SUM(IF(usld.unified_score >= 60,1,0)) as pass_num
        ,SUM(IF(usld.unified_score is null or usld.unified_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list as usl
        JOIN ua_score_list_details as usld on usl.id = usld.score_list_id
        JOIN le_specialty as ls on ls.id = usl.specialty_id
        JOIN le_course as lc on lc.id = usld.course_id
        JOIN ss_unified_process_exam_batch as supeb on usl.unified_process_exam_batch_id = supeb.id
        LEFT JOIN le_student_test as lst ON usl.student_test_Id = lst.id
        WHERE usl.del = 0
        and usl.type = #{query.type}
        and usld.unified_score is not null
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usld.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and supeb.bacth_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and supeb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        GROUP BY usl.pl_id,usl.org_code,usl.unified_process_exam_batch_id,usl.specialty_id,usl.course_id
        order by usl.pl_id asc,usl.unified_process_exam_batch_id asc
    </select>

    <select id="selectComputerExamScoreTable" resultMap="ExamScoreTableExport">
        SELECT
        usl.pl_id
        ,usl.org_code
        ,ls.spe_name as specialty_name
        ,lc.name as course_name
        ,scb.batch_name as bath_name
        ,lst.`name`      as exam_name
        ,scb.exam_end_date as exam_end_time
        ,'机考' as exaeType
        ,count(usld.id) as total_student_num
        ,SUM(IF(usld.composite_score >= 90,1,0)) as nine_num
        ,SUM(IF(usld.composite_score >= 80 and usld.composite_score &lt;= 90,1,0)) as eight_num
        ,SUM(IF(usld.composite_score >= 70 and usld.composite_score &lt;= 80,1,0)) as seven_num
        ,SUM(IF(usld.composite_score >= 60 and usld.composite_score &lt;= 70,1,0)) as six_num
        ,SUM(IF(usld.composite_score is null or usld.composite_score &lt;= 60,1,0))  as less_six_num
        ,SUM(IF(usld.composite_score >= 60,1,0)) as pass_num
        ,SUM(IF(usld.composite_score is null or usld.composite_score &lt; 60,1,0)) as no_pass_num
        FROM ua_score_list as usl
        JOIN ua_score_list_details as usld on usl.id = usld.score_list_id
        JOIN le_specialty as ls on ls.id = usl.specialty_id
        JOIN le_course as lc on lc.id = usld.course_id
        JOIN ss_computer_batch as scb on usl.computer_batch_id = scb.id
        LEFT JOIN le_student_test as lst ON usl.student_test_Id = lst.id
        WHERE usl.del = 0
        and usl.type = #{query.type}
        <if test="query.plId != null">
            and usl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and usl.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and usl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and usld.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and scb.batch_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lst.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examIds != null and query.examIds.size() != 0">
            and scb.id in
            <foreach collection="query.examIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and DATE_FORMAT(usld.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d')
        </if>
        GROUP BY usl.pl_id,usl.org_code,usl.computer_batch_id,usl.specialty_id,usl.course_id
        order by usl.pl_id asc,usl.computer_batch_id asc
    </select>

    <select id="selectStudentAdultScoreList" resultType="com.edu.account.domain.ScoreList">
        select
        sl.*
        from ua_score_list as sl
        left  join ua_platform as p on sl.pl_id = p.id
        left  join ua_school as usc on sl.org_id = usc.id
        left join ae_enroll_student_adult as enroll on sl.enroll_student_id = enroll.id
        left join ua_grade as g on sl.grade_id = g.id
        left join le_course as c on sl.course_id = c.id
        left join le_subject as sub on c.sub_id = sub.id
        left join le_specialty as s on sl.specialty_id = s.id
        where
        sl.del = 0
        and sl.type = '${@com.edu.account.enums.ScoreListTypeEnum@FINAL_EXAM}'
        and p.platform_name = #{item.platName}
        and usc.org_name = #{item.oneOrgName}
        and enroll.student_name = #{item.studentName}
        and enroll.id_number = #{item.idNum}
        and s.spe_name = #{item.specialtyName}
        and g.grade_name = #{item.gradeName}
        and c.name = #{item.courseName}
        and sub.sub_name = #{item.subName}
        <if test="item.studentNumber != null and item.studentNumber != ''">
            and enroll.student_number = #{item.studentNumber}
        </if>
        limit 1
    </select>

    <resultMap id="GraduationRuleScoreVO" type="com.edu.account.model.vo.GraduationRuleScoreVO">
        <result column="course_id" property="courseId"/>
        <result column="course_name" property="courseName"/>
        <result column="credit" property="credit"/>
        <result column="composite_score" property="compositeScore"/>
        <result column="grade_term" property="gradeTerm"/>
        <result column="way" property="way"/>
        <result column="teaching_form" property="teachingForm"/>
        <result column="course_type" property="courseType"/>
        <result column="type" property="type"/>
    </resultMap>
    <select id="getGraduationRuleScore" resultMap="GraduationRuleScoreVO">
        with score_list AS(
            SELECT
                *
            FROM ua_score_list
            WHERE
                del = 0
              and enroll_student_id = #{dto.enrollStudentId}
              and student_id = #{dto.studentId}
        )
        SELECT
            DISTINCT
            t.*,
            lc.name as course_name,
            CONCAT(ug.grade_name,'第',t.term,'学期') as grade_term,
            '考试' as way,
            esa.learn_mod_name as teaching_form
        FROM
            (
                SELECT
                    usl.enroll_student_id as enroll_student_id,
                    usl.student_id as student_id,
                    usld.course_id as course_id,
                    usld.credit as credit,
                    usl.grade_id as grade_id,
                    usld.composite_score as composite_score,
                    usld.grade_term as term,
                    usl.type as type,
                    ut.tag_name as course_type
                FROM
                    ua_score_list_details as usld
                        JOIN score_list as usl ON usld.score_list_id = usl.id
                        JOIN ae_specialty_plan_course as spc ON usld.cj_plan_id = spc.id
                        JOIN ua_tag as ut On spc.course_type = ut.id
                WHERE
                    usld.del = 0
                  and usl.del = 0
                  and usl.type = 'FINAL_EXAM'

                UNION ALL

                SELECT
                    usl.enroll_student_id as enroll_student_id,
                    usl.student_id as student_id,
                    usld.course_id as course_id,
                    usld.credit as credit,
                    usl.grade_id as grade_id,
                    usld.composite_score as composite_score,
                    usld.grade_term as term,
                    usl.type as type,
                    '专业课' as course_type
                FROM
                    ua_score_list_details as usld
                        JOIN score_list as usl ON usld.score_list_id = usl.id
                WHERE
                    usld.del = 0
                  and usl.del = 0
                  and (usl.type = 'UNIFIED_EXAM' or usl.type = 'COMPUTER_EXAM')

                UNION ALL

                SELECT
                    usl.enroll_student_id as enroll_student_id,
                    usl.student_id as student_id,
                    usl.course_id as course_id,
                    0 as credit,
                    usl.grade_id as grade_id,
                    usl.exam_score as composite_score,
                    usl.grade_term_id as term,
                    usl.type as type,
                    '专业课' as course_type
                FROM
                    score_list as usl
                WHERE
                    usl.del = 0
                  and usl.type = 'ALONE_EXAM'
            ) as t
                JOIN le_course as lc ON lc.id = t.course_id
                LEFT JOIN ua_grade as ug ON ug.id = t.grade_id
                LEFT JOIN ae_enroll_student_adult as esa ON esa.id = t.enroll_student_id AND esa.student_id = t.student_id and esa.del = 0
            order by t.course_id asc
    </select>


    <select id="getStudentTestNumber" resultType="java.lang.Long">

        SELECT COUNT(*) AS total_groups
        FROM (
            select  count(id) from le_student_test
            where
            del = 0
            <if test="plId != null">
                and pl_id = #{plId}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="testType != null">
                and type = #{testType}
            </if>
            GROUP BY student_id, course_id, grade_and_batch_id
        ) AS counts;
    </select>
    <select id="getTestBKNumber" resultType="java.lang.Long">
        SELECT
        COUNT(scld.composite_score)
        FROM
        ua_score_list scl
        LEFT JOIN ua_score_list_details scld ON scl.id = scld.score_list_id
        WHERE
            scl.pl_id = #{plId}
        AND scl.org_code = #{orgCode}
        AND scld.score_type = '正考'
        AND scld.composite_score &lt; 60
        AND scld.del = 0
        AND scl.del = 0
        <if test="specialtyId != null">
            AND scl.specialty_id = #{specialtyId}
        </if>
        <if test="levelId != null">
            AND scl.level_id = #{levelId}
        </if>
        <if test="gradeId != null">
            AND scl.grade_id = #{gradeId}
        </if>
        <if test="courseId != null">
            AND scl.course_id = #{courseId}
        </if>
    </select>

    <sql id="selectQueryBindTestCountSql">
        SELECT COUNT(id)
        FROM ae_plan_course_paper
        WHERE subject_id = lc.sub_id
          AND specialty_plan_id = spc.id
          AND del = 0
          AND use_to LIKE
    </sql>
    <select id="selectFinalExamPeopleTableList" resultMap="PeopleTable">
        <if test="query.examType == 1">
            WITH bind_test_count AS (
            SELECT
            spc.id AS spc_id,
            (<include refid="selectQueryBindTestCountSql"/> '%1%' ) AS zkBindTestCount,
            (<include refid="selectQueryBindTestCountSql"/> '%2%' ) AS bkBindTestCount,
            (<include refid="selectQueryBindTestCountSql"/> '%3%' ) AS hkBindTestCount,
            (<include refid="selectQueryBindTestCountSql"/> '%4%' ) AS ckBindTestCount
            FROM
            ae_specialty_plan_course spc
            LEFT JOIN le_course lc ON lc.id = spc.course_id
            )
            SELECT
            asp.pl_id        AS pl_id
            ,asp.org_code     AS org_code
            ,asp.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,asp.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,asp.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,spc.course_id   AS course_id
            ,lc.`name`       AS course_name
            ,asp.id           AS batch_id
            ,asp.plan_name    AS batch_name
            ,IF(exam.zk_name IS NULL, lc.`name`, exam.zk_name) AS exam_name
            ,'正考'           AS score_type
            ,spc.semester    AS grade_term
            FROM
            ae_specialty_plan_course spc
            LEFT JOIN ae_specialty_plan asp ON asp.id = spc.plan_id
            LEFT JOIN bind_test_count ON spc.id = bind_test_count.spc_id
            LEFT JOIN le_course lc ON lc.id = spc.course_id AND lc.del = 0
            LEFT JOIN le_level AS ll ON asp.level_id = ll.id AND ll.del = 0
            LEFT JOIN le_specialty ls ON ls.id = asp.specialty_id AND ls.del = 0
            LEFT JOIN ua_grade ug ON spc.grade_id = ug.id AND ug.del = 0
            LEFT JOIN ae_plan_exam_rule_org AS pero ON
            pero.org_code = spc.org_code AND pero.grade_id = spc.grade_id AND pero.term = spc.semester AND pero.del = 0 AND pero.type = 1 AND pero.exam_rule_type = 1
            LEFT JOIN ae_plan_course_paper_exam_name AS exam ON exam.specialty_plan_id = spc.id AND exam.del = 0
            WHERE
            zkBindTestCount > 0
            AND spc.del = 0
            AND lc.del = 0
            AND asp.del = 0
            AND lc.examine_status = 2
            <include refid="examPeopleOrg" />
            GROUP BY
            asp.pl_id,
            asp.org_code,
            asp.specialty_id,
            asp.grade_id,
            asp.level_id,
            spc.course_id,
            spc.semester
            ORDER BY
            spc.id DESC
        </if>

        <if test="query.examType == 2">
            WITH grouped_count AS (
            SELECT
            COUNT(*) AS ought_number,
            scld.id
            FROM
            ua_score_list scl
            LEFT JOIN ua_score_list_details scld ON scl.id = scld.score_list_id
            LEFT JOIN ae_plan_exam_rule_org ero on scld.cj_rule_id = ero.id
            LEFT JOIN le_course lc ON lc.id = scl.course_id AND lc.del = 0
            LEFT JOIN le_level AS ll ON scl.level_id = ll.id AND ll.del = 0
            LEFT JOIN le_specialty ls ON ls.id = scl.specialty_id AND ls.del = 0
            LEFT JOIN ua_grade ug ON scl.grade_id = ug.id AND ug.del = 0
            WHERE
            scld.del = 0 AND scl.del = 0
            <include refid="examBKPeopleOrg" />
            AND scld.score_type = '正考'
            AND scld.composite_score &lt; 60
            GROUP BY
            scl.pl_id
            ,scl.org_code
            ,scl.specialty_id
            ,scl.level_id
            ,scl.grade_id
            ,scl.course_id
            ,scld.grade_term
            ORDER BY
            scld.id DESC
            )
            SELECT
            scl.pl_id        AS pl_id
            ,scl.org_code     AS org_code
            ,scl.grade_id     AS grade_id
            ,ug.grade_name   AS grade_name
            ,scl.level_id     AS level_id
            ,ll.level_name   AS level_name
            ,scl.specialty_id AS specialty_id
            ,ls.spe_name     AS specialty_name
            ,scl.course_id   AS course_id
            ,lc.`name`       AS course_name
            ,lc.`name` AS exam_name
            ,'补考'           AS score_type
            ,scld.grade_term    AS grade_term
            ,grouped_count.ought_number
            FROM
            ua_score_list scl
            LEFT JOIN ua_score_list_details scld ON scl.id = scld.score_list_id
            LEFT JOIN grouped_count on grouped_count.id = scld.id
            LEFT JOIN ae_plan_exam_rule_org pero on scld.cj_rule_id = pero.id AND pero.del = 0
            LEFT JOIN le_course lc ON lc.id = scl.course_id AND lc.del = 0
            LEFT JOIN le_level AS ll ON scl.level_id = ll.id AND ll.del = 0
            LEFT JOIN le_specialty ls ON ls.id = scl.specialty_id AND ls.del = 0
            LEFT JOIN ua_grade ug ON scl.grade_id = ug.id AND ug.del = 0
            WHERE
            scld.del = 0 AND scl.del = 0
            <include refid="examBKPeopleOrg" />
            AND scld.score_type = '正考'
            AND scld.composite_score &lt; 60
            GROUP BY
            scl.pl_id
            ,scl.org_code
            ,scl.specialty_id
            ,scl.level_id
            ,scl.grade_id
            ,scl.course_id
            ,scld.grade_term
            ORDER BY
            scld.id DESC
        </if>
        <if test="query.examType == 3 or query.examType == 4">
            WITH grouped_count AS (
                SELECT
                COUNT(*) as ought_number,
                apercs.id
                FROM
                ae_plan_exam_rule_course_student apercs
                LEFT JOIN ae_plan_exam_rule_course aperc ON aperc.id = apercs.rule_id
                LEFT JOIN le_course lc ON lc.id = aperc.course_id AND lc.del = 0
                LEFT JOIN ae_specialty_plan asp ON asp.id = aperc.plan_id AND asp.del = 0
                LEFT JOIN le_specialty ls ON ls.id = asp.specialty_id AND ls.del =0
                LEFT JOIN ua_grade ug ON aperc.grade_id = ug.id  AND ug.del = 0
                LEFT JOIN le_level AS ll ON asp.level_id = ll.id  AND ll.del = 0
                WHERE
                apercs.del = 0
                AND aperc.del = 0
                <include refid="examHKPeopleOrg" />
                GROUP BY
                aperc.pl_id,
                aperc.org_code,
                asp.specialty_id,
                aperc.grade_id,
                asp.level_id,
                aperc.course_id,
                aperc.term
                ORDER BY
                apercs.id DESC
            )
            SELECT
            aperc.pl_id AS pl_id
            ,aperc.org_code AS org_code
            ,aperc.grade_id AS grade_id
            ,ug.grade_name AS grade_name
            ,asp.level_id AS level_id
            ,ll.level_name AS level_name
            ,asp.specialty_id AS specialty_id
            ,ls.spe_name AS specialty_name
            ,aperc.course_id AS course_id
            ,lc.`name` AS course_name
            ,asp.id AS batch_id
            ,asp.plan_name AS batch_name
            ,IF(aperc.exam_name IS NULL, lc.`name`, aperc.exam_name ) AS exam_name
            ,IF(aperc.type = 3, '缓考' , '重修') AS score_type
            ,aperc.term AS grade_term
            ,grouped_count.ought_number
            FROM
            ae_plan_exam_rule_course_student apercs
            LEFT JOIN grouped_count on apercs.id = grouped_count.id
            LEFT JOIN ae_plan_exam_rule_course aperc ON aperc.id = apercs.rule_id
            LEFT JOIN le_course lc ON lc.id = aperc.course_id AND lc.del = 0
            LEFT JOIN ae_specialty_plan asp ON asp.id = aperc.plan_id AND asp.del = 0
            LEFT JOIN le_specialty ls ON ls.id = asp.specialty_id AND ls.del =0
            LEFT JOIN ua_grade ug ON aperc.grade_id = ug.id  AND ug.del = 0
            LEFT JOIN le_level AS ll ON asp.level_id = ll.id  AND ll.del = 0
            WHERE
            apercs.del = 0 AND aperc.del = 0
            <include refid="examHKPeopleOrg" />
            GROUP BY
            aperc.pl_id,
            aperc.org_code,
            asp.specialty_id,
            aperc.grade_id,
            asp.level_id,
            aperc.course_id,
            aperc.term
            ORDER BY
            apercs.id DESC

        </if>
    </select>

    <sql id="examHKPeopleOrg">
        <if test="query.examType != null">
            AND  aperc.type = #{query.examType}
        </if>
        <if test="query.plId != null">
            and aperc.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and aperc.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and asp.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and aperc.course_id = #{query.courseId}
        </if>
        <if test="query.examName != null and query.examName != ''">
            and (lc.`name` like CONCAT('%',#{query.examName},'%')
                OR aperc.exam_name like CONCAT('%',#{query.examName},'%')
            )
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(aperc.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            aperc.exam_end_time is null
            or (DATE_FORMAT(aperc.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        <if test="query.grade != null and query.grade.size() != 0">
            and
            <foreach collection="query.grade" item="grade" open="(" separator="or" close=")">
                (aperc.grade_id = #{grade.gradeId}
                and aperc.term in
                <foreach collection="grade.term" item="term" open="(" separator="," close=")">
                    #{term}
                </foreach>
                )
            </foreach>
        </if>
    </sql>

    <sql id="examBKPeopleOrg">
        <if test="query.plId != null">
            and scl.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and scl.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and scl.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and scl.course_id = #{query.courseId}
        </if>
        <if test="query.examName != null and query.examName != ''">
            and lc.`name` like CONCAT('%',#{query.examName},'%')
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(pero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            pero.exam_end_time is null
            or (DATE_FORMAT(pero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        <if test="query.grade != null and query.grade.size() != 0">
            and
            <foreach collection="query.grade" item="grade" open="(" separator="or" close=")">
                (scl.grade_id = #{grade.gradeId}
                and scld.semester in
                <foreach collection="grade.term" item="term" open="(" separator="," close=")">
                    #{term}
                </foreach>
                )
            </foreach>
        </if>
    </sql>

    <select id="getRealityNumber" resultType="java.lang.Long">
        SELECT
            count(DISTINCT tes.course_id, tes.student_id)
        FROM
            le_student_test tes
            LEFT JOIN ae_enroll_student_adult adu ON adu.student_id = tes.student_id
            AND adu.grade_id = tes.grade_and_batch_id
            AND adu.del = 0
        WHERE
            tes.del = 0
          AND tes.org_code = #{orgCode}
          AND tes.pl_id = #{plId}
          AND tes.type= #{testType}
          AND adu.specialty_id = #{specialtyId}
          AND adu.level_id = #{levelId}
          AND adu.grade_id = #{gradeId}
          AND tes.course_id = #{courseId}
    </select>

    <sql id="examPeopleOrg">
        <if test="query.plId != null">
            and asp.pl_id = #{query.plId }
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            and asp.org_code = #{query.orgCode}
        </if>
        <if test="query.specialtyId != null">
            and asp.specialty_id = #{query.specialtyId}
        </if>
        <if test="query.courseId != null">
            and spc.course_id = #{query.courseId}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and asp.plan_name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.examName != null and query.examName != ''">
            and (exam.zk_name like CONCAT('%',#{query.examName},'%')
                or exam.bk_name like CONCAT('%',#{query.examName},'%')
                or exam.hk_name like CONCAT('%',#{query.examName},'%')
                or exam.ck_name like CONCAT('%',#{query.examName},'%')
                or lc.`name` like CONCAT('%',#{query.examName},'%')
                )
        </if>
        <if test="query.examStartTime != null">
            and DATE_FORMAT(pero.exam_start_time,'%Y-%m-%d') >= DATE_FORMAT(#{query.examStartTime},'%Y-%m-%d')
        </if>
        <if test="query.examEndTime != null">
            and (
            pero.exam_end_time is null
            or (DATE_FORMAT(pero.exam_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.examEndTime},'%Y-%m-%d'))
            )
        </if>
        <if test="query.grade != null and query.grade.size() != 0">
            and
            <foreach collection="query.grade" item="grade" open="(" separator="or" close=")">
                (spc.grade_id = #{grade.gradeId}
                and spc.semester in
                <foreach collection="grade.term" item="term" open="(" separator="," close=")">
                    #{term}
                </foreach>
                )
            </foreach>
        </if>
    </sql>

    <select id="selectSchoolRatio" resultType="com.edu.adult.domain.OrgResultRatio">
        select * from ae_org_result_ratio where org_code  = #{orgCode} order by create_time limit 1
    </select>

    <select id="selectOfflineCourse" resultType="com.edu.account.domain.OfflineScoreList">
        select * from ua_offline_score_list where grade_id = #{gradeId}
                                              and  specialty_id = #{specialtyId} and  course_id = #{courseId} order by create_time limit 1
    </select>

    <select id="selectPlanCourseRatio" resultType="com.edu.adult.domain.SpecialtyPlanCourse">
        select aspc.*
        from ae_specialty_plan_course aspc
        left join ae_specialty_plan asp on asp.id = aspc.plan_id
        where aspc.org_code = #{orgCode}
          and aspc.course_id = #{courseId}
          and aspc.semester = #{gradeTerm}
          and asp.grade_id = #{gradeId}
          and asp.specialty_id = #{specialtyId}
          and asp.level_id = #{levelId}
        order by aspc.create_time desc
        limit 1
    </select>

    <select id="selectScoreVoList" resultType="com.edu.adult.model.vo.shcool.pc.StudentAdultStudyVo">
        SELECT a.id,
            a.student_id AS studentId,
            a.student_name AS studentName,
            a.id_number AS idNumber,
            a.nation AS nation,
            a.school_sys AS schoolSys,
            CASE
            WHEN a.sex = 'WOMAN' THEN '女'
            WHEN a.sex = 'MAN' THEN '男'
            ELSE '未知'
            END AS sex,
            a.pl_id AS plId,
            a.batch_id AS batchId,
            a.one_org_code AS oneOrgCode,
            a.two_org_code AS twoOrgCode,
            getOrgName(a.one_org_code) as oneOrgName,
            a.student_number AS studentNumber,
            ug.grade_name AS gradeName,
            l2.spe_name AS specialtyName,
            a.specialty_id AS specialtyId,
            ll.level_name,
            gt.term_start,
            gt.term_end,
            gt.term_num,
            cou.name as courseName,
            ae1.composite_score as compositeScore
        FROM ae_enroll_student_adult a
        LEFT JOIN ua_grade ug ON a.grade_id = ug.id
        LEFT JOIN le_level ll ON a.level_id = ll.id
        LEFT JOIN le_specialty l2 ON a.specialty_id = l2.id
        left join ae_study_state_assess ae1 on a.id = ae1.roll_id and ae1.del = FALSE
        left join ua_grade_term gt on gt.grade_id = ae1.grade_id and gt.term_num = ae1.semester and gt.del = 0
        left join le_course cou on cou.id = ae1.course_id and cou.del = FALSE
        <where>
            a.del = 0
            and a.student_roll_state in ('REGISTER', 'REPORT')
            <if test="enrollStudentIds != null and enrollStudentIds.size() > 0">
               and a.id in
                <foreach collection="enrollStudentIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.id desc
    </select>
</mapper>





