<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>乐山师范学院成人高等教育学生成绩表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        body {
            font-family: "Source Han Sans CN", serif;
            position: relative;
            font-size: 10px;
        }
        .table-border {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        .table-border th,
        .table-border td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        .header-info {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .header-info td {
            padding: 6px;
        }
        .title {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        @media print {
            .title {
                font-size: 10px;
            }
        }
        .seal {
            position: absolute;
            right: 80px;
            bottom: 100px;
            width: 120px;
            height: 120px;
        }
        .signature-area {
            margin-top: 30px;
            width: 100%;
            border-collapse: collapse;
        }
        @media print {
            @page {
                size: landscape !important;
                margin: 5mm !important;
            }
            body {
                font-size: 9px !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            * {
                box-sizing: border-box !important;
            }
            table {
                width: 100% !important;
                table-layout: fixed !important;
            }
            tr{
                height:30px;
            }
            td {
                display: block;
                word-wrap: break-word !important;
                padding:6px;
                text-align: center;
            }
        }
        .fixed-width {
            border: 1px solid #000;
            text-align: center;
            width: 170px !important; /* 设置固定宽度 */
        }
        .fixed-score {
            border: 1px solid #000;
            text-align: center;
            white-space: nowrap;
            width: 60px !important; /* 设置固定宽度 */
        }
        .fixed-width-title {
            white-space: nowrap;
        }
    </style>
</head>
<body>
<div id="content">
    <h1 class="title" style="margin-top:60px; text-align-all: center">乐山师范学院成人高等教育学生成绩表</h1>

    <!-- 学生基本信息 -->
    <table
            id="myTable"
            style="
        border-collapse: collapse;
        width: 100%;
        table-layout: fixed;
        border: 1px solid #000;
        margin: 10px 0;
      "
    >
        <tr style="height:40px;">
            <td style="border: 1px solid #000;  text-align: center">
                <strong>姓 名</strong>
            </td>
            <td id="name" colspan="3" style="border: 1px solid #000; text-align: center">${studentInfo.studentName!''}</td>
            <td  colspan="2" style="border: 1px solid #000; text-align: center">
                <strong>性 别</strong>
            </td>
            <td id="gender" style="border: 1px solid #000; text-align: center">${studentInfo.sex!''}</td>
            <td style="border: 1px solid #000; text-align: center"><strong>民 族</strong></td>
            <td id="nationality" style="border: 1px solid #000; text-align: center">${studentInfo.nation!''!''}</td>
            <td colspan="2" style="border: 1px solid #000; text-align: center">
                <strong>身份证号</strong>
            </td>
            <td id="idCard" colspan="5" style="border: 1px solid #000; text-align: center">${studentInfo.idNumber!''}</td>
            <td style="border: 1px solid #000;text-align: center">
                <strong>专 业</strong>
            </td>
            <td id="major" colspan="4" style="border: 1px solid #000; text-align: center">${studentInfo.specialtyName!''}</td>
        </tr>
        <tr  style="height:40px;">
            <td style="border: 1px solid #000; text-align: center">
                <strong>学 号</strong>
            </td>
            <td id="studentNumber" colspan="3" style="border: 1px solid #000; text-align: center">
                ${studentInfo.studentNumber!''}
            </td>
            <td colspan="2" style="border: 1px solid #000; text-align: center">
                <strong>教学单位</strong>
            </td>
            <td id="department" colspan="5" style="border: 1px solid #000; text-align: center">
                ${studentInfo.oneOrgName!''}
            </td>
            <td style="border: 1px solid #000; text-align: center">
                <strong>学 制</strong>
            </td>
            <td id="educationLength" style="border: 1px solid #000; text-align: center">
                ${studentInfo.schoolSys!''}
            </td>
            <td  style="border: 1px solid #000; text-align: center">
                <strong>层 次</strong>
            </td>
            <td  id="level"colspan="2" style="border: 1px solid #000;text-align: center">${studentInfo.levelName!''}</td>
            <td style="border: 1px solid #000; text-align: center">
                <strong>年 级</strong>
            </td>
            <td id="grade" colspan="4" style="border: 1px solid #000; text-align: center">${studentInfo.gradeName!''}</td>

        </tr>
        <!-- 成绩表格部分 -->
        <tr id="dataRange" style="height:40px;">
            <td rowspan="3" style="border: 1px solid #000;  text-align: center">
                <strong>序 号</strong>
            </td>
            <td colspan="8" style="border: 1px solid #000;  text-align: center">
                ${studentInfo.schoolOneYear!''}
            </td>
            <td colspan="8" style="border: 1px solid #000;  text-align: center">
                ${studentInfo.schoolTwoYear!''}
            </td>
            <td colspan="4" style="border: 1px solid #000;  text-align: center">
                ${studentInfo.schoolThreeYear!''}
            </td>
        </tr>
        <tr style="height:40px;">
            <td colspan="4" style="border: 1px solid #000; text-align: center">
                <strong>上学期</strong>
            </td>
            <td colspan="4" style="border: 1px solid #000;  text-align: center">
                <strong>下学期</strong>
            </td>
            <td colspan="4" style="border: 1px solid #000;  text-align: center">
                <strong>上学期</strong>
            </td>
            <td colspan="4" style="border: 1px solid #000; text-align: center">
                <strong>下学期</strong>
            </td>
            <td colspan="4" style="border: 1px solid #000;  text-align: center">
                <strong>上学期</strong>
            </td>
        </tr>
        <tr style="height:40px;">
            <td colspan="3" class="fixed-width fixed-width-title">
                课程名称
            </td>
            <td class="fixed-score">
                成绩
            </td>
            <td colspan="3" class="fixed-width fixed-width-title">
                课程名称
            </td>
            <td class="fixed-score">
                成绩
            </td>
            <td colspan="3" class="fixed-width fixed-width-title">
                课程名称
            </td>
            <td class="fixed-score">
                成绩
            </td>
            <td colspan="3" class="fixed-width fixed-width-title">
                课程名称
            </td>
            <td class="fixed-score">
                成绩
            </td>
            <td colspan="3" class="fixed-width fixed-width-title">
                课程名称
            </td>
            <td class="fixed-score">
                成绩
            </td>
        </tr>
        <#list scoreList as scoreItem>
        <tr style="height:40px;">
            <td style="border: 1px solid #000;  text-align: center">
                ${scoreItem.indexNum!}
            </td>
            <td colspan="3" class="fixed-width">
                ${scoreItem.courseName1!''}
            </td>
            <td class="fixed-score">
                ${scoreItem.score1!''}
            </td>
            <td colspan="3" class="fixed-width">
                ${scoreItem.courseName2!''}
            </td>
            <td class="fixed-score">
                ${scoreItem.score2!''}
            </td>
            <td colspan="3" class="fixed-width">
                ${scoreItem.courseName3!''}
            </td>
            <td class="fixed-score">
                ${scoreItem.score3!''}
            </td>
            <td colspan="3" class="fixed-width">
                ${scoreItem.courseName4!''}
            </td>
            <td class="fixed-score">
                ${scoreItem.score4!''}
            </td>
            <td colspan="3" class="fixed-width">
                ${scoreItem.courseName5!''}
            </td>
            <td class="fixed-score">
                ${scoreItem.score5!''}
            </td>
        </tr>
        </#list>
        <!-- 签名区域 -->
        <tr id="sign-area" style="height:80px;">
            <td  style="border: 1px solid #000;text-align: center">
                <strong>经办人</strong>
            </td>
            <td
                    colspan="3"
                    style="
            border: 1px solid #000;
            text-align: center;
            word-wrap: break-word;
          "
            >

            </td>
            <td colspan="4" style="border: 1px solid #000;text-align: center">
                <strong>负责人签章</strong>
            </td>
            <td colspan="4" style="border: 1px solid #000;text-align: center">

            </td>
            <td colspan="4" style="border: 1px solid #000;text-align: center">
                <strong>主管部门盖章</strong>
            </td>
            <td colspan="5" style="border: 1px solid #000; text-align: center">

            </td>
        </tr>
    </table>
</div>

</body>
</html>
