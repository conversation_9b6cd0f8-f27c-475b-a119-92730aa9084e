package com.edu.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.account.domain.FinanceSettlementStudent;
import com.edu.account.model.query.FinanceSettlementStudentQuery;
import com.edu.account.model.vo.FinanceSettlementStudentVo;
import com.edu.commons.model.commons.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算学员明细管理服务接口
 * @email <EMAIL>
 * @date 2025-07-25
 */
public interface FinanceSettlementStudentService extends IService<FinanceSettlementStudent> {

    /**
     * 分页查询结算学员明细列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<FinanceSettlementStudentVo> getPageList(FinanceSettlementStudentQuery query);

    /**
     * 根据结算批次ID获取学员明细列表
     *
     * @param settlementBatchId 结算批次ID
     * @return 学员明细列表
     */
    List<FinanceSettlementStudentVo> getBySettlementBatchId(Long settlementBatchId);

    /**
     * 根据2级机构Code获取学员明细列表
     *
     * @param twoOrgCode 2级机构Code
     * @return 学员明细列表
     */
    List<FinanceSettlementStudentVo> getBySettlementOrgId(String twoOrgCode);

    /**
     * 同步学员缴费数据
     *
     * @param settlementBatchId 结算批次ID
     */
    void syncStudentDataApply(Long settlementBatchId);

    /**
     * 同步学员缴费数据
     *
     * @param settlementBatchId 结算批次ID
     */
    void syncStudentDataProcess(Long settlementBatchId,Long settlementBusinessId);

    /**
     * 删除学员明细
     *
     * @param id 明细ID
     */
    void deleteStudentDetail(Long id);

    /**
     * 批量删除学员明细
     *
     * @param ids 明细ID列表
     */
    void batchDeleteStudentDetail(List<Long> ids);

    /**
     * 导出学员明细数据
     *
     * @param settlementBatchId 结算批次ID
     * @return 导出文件字节数组
     */
    byte[] exportStudentDetails(Long settlementBatchId);

    /**
     * 按机构导出学员明细数据
     *
     * @param twoOrgCode 结算机构ID
     * @return 导出文件字节数组
     */
    byte[] exportStudentDetailsByOrgCode(String twoOrgCode);

    /**
     * 获取学员明细统计信息
     *
     * @param settlementBatchId 结算批次ID
     * @return 统计信息
     */
    StudentDetailStatistics getStatistics(Long settlementBatchId);

    /**
     * 获取机构学员明细统计信息
     *
     * @param settlementOrgId 结算机构ID
     * @return 统计信息
     */
    StudentDetailStatistics getStatisticsByOrg(Long settlementOrgId);

    /**
     * 验证学员明细数据
     *
     * @param settlementBatchId 结算批次ID
     * @return 验证结果
     */
    ValidationResult validateStudentDetails(Long settlementBatchId);

    /**
     * 学员明细统计信息
     */
    class StudentDetailStatistics {
        private Integer totalStudentCount;
        private java.math.BigDecimal totalShouldAmount;
        private java.math.BigDecimal totalPaidAmount;
        private java.math.BigDecimal totalRefundAmount;
        private Integer orgCount;

        // getters and setters
        public Integer getTotalStudentCount() { return totalStudentCount; }
        public void setTotalStudentCount(Integer totalStudentCount) { this.totalStudentCount = totalStudentCount; }
        public java.math.BigDecimal getTotalShouldAmount() { return totalShouldAmount; }
        public void setTotalShouldAmount(java.math.BigDecimal totalShouldAmount) { this.totalShouldAmount = totalShouldAmount; }
        public java.math.BigDecimal getTotalPaidAmount() { return totalPaidAmount; }
        public void setTotalPaidAmount(java.math.BigDecimal totalPaidAmount) { this.totalPaidAmount = totalPaidAmount; }
        public java.math.BigDecimal getTotalRefundAmount() { return totalRefundAmount; }
        public void setTotalRefundAmount(java.math.BigDecimal totalRefundAmount) { this.totalRefundAmount = totalRefundAmount; }
        public Integer getOrgCount() { return orgCount; }
        public void setOrgCount(Integer orgCount) { this.orgCount = orgCount; }
    }

    /**
     * 验证结果
     */
    class ValidationResult {
        private Boolean isValid;
        private List<String> errorMessages;
        private List<String> warningMessages;

        // getters and setters
        public Boolean getIsValid() { return isValid; }
        public void setIsValid(Boolean isValid) { this.isValid = isValid; }
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
        public List<String> getWarningMessages() { return warningMessages; }
        public void setWarningMessages(List<String> warningMessages) { this.warningMessages = warningMessages; }
    }
}
