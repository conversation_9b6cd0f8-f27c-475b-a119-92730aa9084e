package com.edu.account.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.account.domain.GradeTerm;
import com.edu.account.domain.Platform;
import com.edu.account.domain.ScoreList;
import com.edu.account.domain.ScoreListDetails;
import com.edu.account.enums.ScoreListTypeEnum;
import com.edu.account.excel.ComputerExamScoreImport;
import com.edu.account.excel.FinalExamScoreImport;
import com.edu.account.excel.UnifiedExamScoreImport;
import com.edu.account.excel.export.*;
import com.edu.account.manager.PlatformManager;
import com.edu.account.mapper.ScoreListDetailsMapper;
import com.edu.account.mapper.ScoreListMapper;
import com.edu.account.model.dto.CheckExamPlanDTO;
import com.edu.account.model.dto.StudentConditionDTO;
import com.edu.account.model.dto.school.GraduationRuleDTO;
import com.edu.account.model.query.ExaminationStatisticsQuery;
import com.edu.account.model.query.ScoreListDetailsQuery;
import com.edu.account.model.query.ScoreListQuery;
import com.edu.account.model.vo.GraduationRuleScoreVO;
import com.edu.account.model.vo.ScoreListDetailsVo;
import com.edu.account.model.vo.ScoreListVo;
import com.edu.account.model.vo.ScoreVO;
import com.edu.account.service.GradeTermService;
import com.edu.account.service.ScoreListDetailsService;
import com.edu.account.service.ScoreListService;
import com.edu.account.transfer.DocumentTransferWithItext;
import com.edu.adult.domain.EnrollStudentAdult;
import com.edu.adult.domain.SpecialtyPlanCourse;
import com.edu.adult.feign.EnrollStudentAdultFeign;
import com.edu.adult.model.dto.shool.pc.PlanExamRuleOrgTermDTO;
import com.edu.adult.model.query.EnrollStudentAdultQuery;
import com.edu.adult.model.query.SpecialtyPlanCourseQuery;
import com.edu.adult.model.query.SpecialtyPlanSelectQuery;
import com.edu.adult.model.query.student.pc.StudentMyExamQuery;
import com.edu.adult.model.vo.shcool.pc.StudentAdultStudyVo;
import com.edu.adult.model.vo.student.pc.PlanBindTestPaperVo;
import com.edu.common.manager.NameManager;
import com.edu.commons.exception.BaseException;
import com.edu.commons.exception.ExcelException;
import com.edu.commons.handler.FreeMarkerHolder;
import com.edu.commons.model.Ret;
import com.edu.commons.model.commons.PageInfo;
import com.edu.commons.model.enums.MimeTypeEnum;
import com.edu.commons.utils.BigDecimalUtils;
import com.edu.commons.utils.ConditionAssert;
import com.edu.commons.utils.excel.CustomSheetWriteHandler;
import com.edu.commons.utils.excel.ExcelUtils;
import com.edu.lessons.enums.StudentTestTypeEnum;
import com.edu.lessons.feign.SpecialtyPlanFeign;
import com.edu.lessons.feign.StudentTestFeign;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @DESCRIPTION 成绩管理Service
 * @email <EMAIL>
 * 2023-10-31
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ScoreListServiceImpl extends ServiceImpl<ScoreListMapper, ScoreList> implements ScoreListService {
    private final ScoreListDetailsService scoreListDetailsService;
    private final ScoreListDetailsMapper scoreListDetailsMapper;
    private final PlatformManager platformManager;
    private final NameManager nameManager;

    private final EnrollStudentAdultFeign enrollStudentAdultFeign;
    private final StudentTestFeign studentTestFeign;
    private final SpecialtyPlanFeign specialtyPlanFeign;
    private final GradeTermService gradeTermService;

    private  Map<String, List<EnrollStudentAdult>> stringListMap = new HashMap<>();
    private  Map<String, List<PlanBindTestPaperVo>> testPaperVoMap = new HashMap<>();
    private  Map<String, List<GradeTerm>> gradeTermMap = new HashMap<>();
    private  Map<String, List<SpecialtyPlanCourse>> planCourseMap = new HashMap<>();
    private  Map<String, List<PlanExamRuleOrgTermDTO>> ruleOrgTermMap = new HashMap<>();

    @Override
    public PageInfo<ScoreListVo> pageList(ScoreListQuery query) {
        TimeInterval timer = new TimeInterval();
        PageInfo<Long> pageIds = PageInfo.of(query, () -> baseMapper.selectRelationshipQuery(query));
        List<ScoreListVo> list = baseMapper.selectQuery(pageIds.getList(),query.getType());
        PageInfo<ScoreListVo> pageInfo = PageInfo.result(pageIds, list);
        log.info("查询成绩列表耗时：{}", timer.interval());
        timer.intervalRestart();

        nameManager.fillCreateName(pageInfo, true);
        nameManager.fillPlatName(pageInfo.getList(), "plId", "platName");
        nameManager.fillOrgName(pageInfo.getList(), "orgCode", "schoolName");
        nameManager.fillOrgName(pageInfo.getList(), "orgCode", "orgName");
        // 综合分数加上线下成绩
//        Optional.ofNullable(query.getOrgCode()).ifPresent(t->{
//           OrgResultRatio ratio = baseMapper.selectSchoolRatio(t);
//           if (ratio != null && CollectionUtils.isNotEmpty(pageInfo.getList())) {
//               pageInfo.getList().forEach(vo->{
//                   BigDecimal synthesizeScore = vo.getSynthesizeScore();
//                   if (synthesizeScore == null) {
//                       synthesizeScore = new BigDecimal(0);
//                       BigDecimal onlineComprehensiveScore = ratio.getOnlineComprehensiveScore();
//                       Long specialtyId = vo.getSpecialtyId();
//                       Long courseId = vo.getCourseId();
//                       Long gradeId = vo.getGradeId();
//                       OfflineScoreList scoreList = baseMapper.selectOfflineCourse(gradeId,specialtyId,courseId);
//                       if (scoreList != null) {
//                           BigDecimal offlineScore = ratio.getOfflineDiscussion().multiply(scoreList.getOfflineDiscussionScore()).
//                                   add(ratio.getOfflineExamResult().multiply(scoreList.getOfflineExamScore())).
//                                   add(ratio.getOfflineOther().multiply(scoreList.getOfflineOtherScore())).
//                                   add(ratio.getOfflineTeaching().multiply(scoreList.getOfflineTeachingScore()));
//                           // 线下+线上
//                           vo.setSynthesizeScore(synthesizeScore.multiply(onlineComprehensiveScore).add(offlineScore));
//                       } else {
//                           vo.setSynthesizeScore(synthesizeScore.multiply(onlineComprehensiveScore));
//                       }
//                   }
//               });
//           }
//        });
        log.info("查询成绩列表耗时：{}", timer.interval());
        return pageInfo;
    }


    @Override
    public void insertData(ScoreList scoreList) {
        log.info("开始处理成绩数据,请求参数:{}", JSONObject.toJSONString(scoreList));

        try {
            // 查询是否已存在相同条件的成绩记录
            ScoreList existingScore = findExistingScore(scoreList);

            ScoreList scoreBD;
            if (existingScore == null) {
                // 新建成绩
                log.info("未找到已存在的成绩记录，创建新记录");
                if (saveScoreList(scoreList)) {
                    log.info("成绩数据处理完成：新建成绩并提前结束处理");
                    return;
                }
                scoreBD = scoreList;
            } else {
                // 修改成绩
                log.info("找到已存在的成绩记录，ID={}", existingScore.getId());
                updateScoreList(scoreList, existingScore);
                scoreBD = existingScore;
            }

            // 独立考试类型直接结束处理
            if (scoreList.getType() == ScoreListTypeEnum.ALONE_EXAM) {
                log.info("独立考试类型，结束处理成绩数据");
                return;
            }

            // 处理成绩详情
            boolean detailProcessed = updateScoreListDetail(scoreList, scoreBD);
            if (detailProcessed) {
                log.info("成绩详情处理完成");
            }
        } catch (Exception e) {
            log.error("处理成绩数据失败: {}", e.getMessage(), e);
            throw new BaseException("处理成绩数据失败: " + e.getMessage());
        }
    }

    /**
     * 查找已存在的成绩记录
     *
     * @param scoreList 成绩列表对象
     * @return 已存在的成绩记录，不存在则返回null
     */
    private ScoreList findExistingScore(ScoreList scoreList) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(ScoreList::getStudentTestId, scoreList.getStudentTestId())
                .eq(ScoreList::getType, scoreList.getType())
                .eq(ScoreList::getStudentId, scoreList.getStudentId())
                .eq(ScoreList::getPlId, scoreList.getPlId())
                .eq(ScoreList::getOrgId, scoreList.getOrgId())
                .eq(ScoreList::getSchoolId, scoreList.getSchoolId())
                .eq(scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM, ScoreList::getGradeId, scoreList.getGradeId())
                .eq(scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM, ScoreList::getGradeTermId, scoreList.getGradeTermId())
                .eq(scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM, ScoreList::getSubId, scoreList.getSubId())
                .eq(scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM, ScoreList::getCourseId, scoreList.getCourseId())
                .eq(scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM, ScoreList::getScoreType, scoreList.getScoreType())
                .eq(scoreList.getType() == ScoreListTypeEnum.UNIFIED_EXAM, ScoreList::getUnifiedProcessExamBatchId, scoreList.getUnifiedProcessExamBatchId())
                .eq(scoreList.getType() == ScoreListTypeEnum.COMPUTER_EXAM, ScoreList::getComputerBatchId, scoreList.getComputerBatchId())
                .eq(scoreList.getType() == ScoreListTypeEnum.ALONE_EXAM, ScoreList::getAloneExamId, scoreList.getAloneExamId())
                .eq(ObjectUtils.isNotEmpty(scoreList.getSpecialtyId()), ScoreList::getSpecialtyId, scoreList.getSpecialtyId())
                .eq(ObjectUtils.isNotEmpty(scoreList.getLevelId()), ScoreList::getLevelId, scoreList.getLevelId())
                .eq(ScoreList::isDel, false)
                .last("limit 1")
                .one();
    }

    /**
     * 更新成绩列表详情
     *
     * @param scoreList 新的成绩列表对象
     * @param existingScore 已存在的成绩列表对象
     * @return 是否成功处理成绩详情
     */
    private boolean updateScoreListDetail(ScoreList scoreList, ScoreList existingScore) {
        try {
            ScoreListDetails newDetail = scoreList.getScoreListDetails();
            if (newDetail == null) {
                log.warn("成绩详情为空，无法更新: studentId={}", scoreList.getStudentId());
                return false;
            }

            // 查询是否已存在成绩详情
            ScoreListDetails existingDetail = findExistingScoreDetail(existingScore.getId(), newDetail, scoreList.getType());

            // 如果在校成绩已经导入了成绩，则不更新
            if (existingDetail != null &&
                    scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM &&
                    existingDetail.getDataSources() != null &&
                    "导入成绩".equals(existingDetail.getDataSources())) {
                log.info("在校成绩已通过导入方式录入，不进行更新: studentId={}, courseId={}",
                        scoreList.getStudentId(), newDetail.getCourseId());
                return true;
            }

            if (existingDetail == null) {
                // 不存在则创建新的成绩详情
                return createNewScoreDetail(newDetail, existingScore.getId(), scoreList);
            } else {
                // 存在则更新成绩详情
                return updateExistingScoreDetail(newDetail, existingDetail, scoreList);
            }
        } catch (Exception e) {
            log.error("更新成绩详情失败: {}", e.getMessage(), e);
            throw new BaseException("更新成绩详情失败: " + e.getMessage());
        }
    }

    /**
     * 查找已存在的成绩详情
     *
     * @param scoreListId 成绩列表ID
     * @param detail 成绩详情
     * @param type 成绩类型
     * @return 已存在的成绩详情，不存在则返回null
     */
    private ScoreListDetails findExistingScoreDetail(Long scoreListId, ScoreListDetails detail, ScoreListTypeEnum type) {
        return new LambdaQueryChainWrapper<>(scoreListDetailsMapper)
                .eq(ScoreListDetails::getScoreListId, scoreListId)
                .eq(ScoreListDetails::getCourseId, detail.getCourseId())
                .eq(ScoreListDetails::getScoreType, detail.getScoreType())
                .eq(type == ScoreListTypeEnum.FINAL_EXAM, ScoreListDetails::getGradeTerm, detail.getGradeTerm())
                .eq(ScoreListDetails::isDel, false)
                .last("limit 1")
                .one();
    }

    /**
     * 创建新的成绩详情
     *
     * @param detail 成绩详情
     * @param scoreListId 成绩列表ID
     * @param scoreList 成绩列表
     * @return 是否成功创建
     */
    private boolean createNewScoreDetail(ScoreListDetails detail, Long scoreListId, ScoreList scoreList) {
        detail.setCreateTime(new Date());
        detail.setScoreListId(scoreListId);

        // 根据成绩类型处理不同的成绩计算
        if (scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM) {
            updateCjScore(detail, detail.getExamScore());
        } else if (scoreList.getType() == ScoreListTypeEnum.UNIFIED_EXAM ||
                scoreList.getType() == ScoreListTypeEnum.COMPUTER_EXAM) {
            updateZkScore(detail,
                    scoreList.getType(),
                    scoreList.getType() == ScoreListTypeEnum.UNIFIED_EXAM ?
                            detail.getUnifiedScore() :
                            detail.getComputerScore());
        }

        // 处理免考申请
        if (scoreList.getIsExemptionApplication()) {
            detail.setCompositeScore(new BigDecimal(60));
        }

        scoreListDetailsMapper.insert(detail);
        log.info("成功创建成绩详情: studentId={}, courseId={}, scoreType={}",
                scoreList.getStudentId(), detail.getCourseId(), detail.getScoreType());
        return true;
    }

    /**
     * 更新已存在的成绩详情
     *
     * @param newDetail 新的成绩详情
     * @param existingDetail 已存在的成绩详情
     * @param scoreList 成绩列表
     * @return 是否成功更新
     */
    private boolean updateExistingScoreDetail(ScoreListDetails newDetail, ScoreListDetails existingDetail, ScoreList scoreList) {
        newDetail.setId(existingDetail.getId());
        newDetail.setUpdateTime(new Date());

        // 根据成绩类型处理不同的成绩计算
        switch (scoreList.getType()) {
            case UNIFIED_EXAM ->
                    updateZkScore(newDetail, scoreList.getType(), newDetail.getUnifiedScore());
            case COMPUTER_EXAM ->
                    updateZkScore(newDetail, scoreList.getType(), newDetail.getComputerScore());
            case FINAL_EXAM ->
                    updateCjScore(newDetail, newDetail.getExamScore());
        }

        scoreListDetailsMapper.updateById(newDetail);
        log.info("成功更新成绩详情: studentId={}, courseId={}, scoreType={}",
                scoreList.getStudentId(), newDetail.getCourseId(), newDetail.getScoreType());
        return false;
    }

    /**
     * 保存成绩列表信息
     *
     * @param scoreList 成绩列表对象
     * @return 是否提前结束处理
     */
    private boolean saveScoreList(ScoreList scoreList) {
        // 设置初始状态和创建时间
        scoreList.setIsPublish(0);
        scoreList.setCreateTime(new Date());

        try {
            // 保存主表信息
            baseMapper.insert(scoreList);

            // 获取成绩详情并设置关联ID
            ScoreListDetails scoreListDetails = scoreList.getScoreListDetails();
            if (scoreListDetails != null) {
                scoreListDetails.setScoreListId(scoreList.getId());

                // 查询是否有线下成绩，有的话设置线下数据
                checkOfflineResults(scoreListDetails, scoreList);
            }

            // 独立考试类型直接返回
            if (scoreList.getType() == ScoreListTypeEnum.ALONE_EXAM) {
                return false;
            }

            // 处理统一考试或计算机考试类型
            if (scoreList.getType() == ScoreListTypeEnum.UNIFIED_EXAM || scoreList.getType() == ScoreListTypeEnum.COMPUTER_EXAM) {

                // 处理免考申请
                if (scoreList.getIsExemptionApplication()) {
                    scoreList.getScoreListDetails().setCompositeScore(new BigDecimal(60));
                    updateZkScore(
                            scoreListDetails,
                            scoreList.getType(),
                            scoreList.getType() == ScoreListTypeEnum.UNIFIED_EXAM ?
                                    scoreList.getScoreListDetails().getUnifiedScore() :
                                    scoreList.getScoreListDetails().getCompositeScore()
                    );
                } else {
                    // 正常考试成绩处理
                    updateZkScore(
                            scoreListDetails,
                            scoreList.getType(),
                            scoreList.getType() == ScoreListTypeEnum.UNIFIED_EXAM ?
                                    scoreList.getScoreListDetails().getUnifiedScore() :
                                    scoreList.getScoreListDetails().getComputerScore()
                    );
                }

                // 保存成绩详情
                scoreListDetailsMapper.insert(scoreList.getScoreListDetails());
                log.info("成绩数据处理完成: studentId={}, courseId={}",
                        scoreList.getStudentId(),
                        scoreListDetails != null ? scoreListDetails.getCourseId() : null);
                return true;
            }
        } catch (Exception e) {
            log.error("保存成绩数据失败: {}", e.getMessage(), e);
            throw new BaseException("保存成绩数据失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 更新成绩列表信息
     *
     * @param scoreList 新的成绩列表对象
     * @param scoreBD 已存在的成绩列表对象
     */
    private void updateScoreList(ScoreList scoreList, ScoreList scoreBD) {
        try {
            if (scoreList.getType() == ScoreListTypeEnum.ALONE_EXAM) {
                // 独立考试类型，只有当新成绩大于等于旧成绩时才更新
                if (ObjectUtils.isNotEmpty(scoreBD.getExamScore()) &&
                        !(scoreBD.getExamScore().compareTo(scoreList.getExamScore()) > 0)) {
                    log.debug("更新独立考试成绩: studentId={}, oldScore={}, newScore={}",
                            scoreList.getStudentId(),
                            scoreBD.getExamScore(),
                            scoreList.getExamScore());

                    scoreBD.setExamScore(scoreList.getExamScore());
                    scoreBD.setStudentTestId(scoreList.getStudentTestId());
                    scoreBD.setUpdateTime(new Date());
                    baseMapper.updateById(scoreBD);
                } else {
                    log.debug("独立考试成绩未更新，新成绩小于旧成绩: studentId={}, oldScore={}, newScore={}",
                            scoreList.getStudentId(),
                            scoreBD.getExamScore(),
                            scoreList.getExamScore());
                }
            } else if (scoreList.getType() == ScoreListTypeEnum.FINAL_EXAM) {
                // 在校成绩类型，更新在校成绩并检查在校成绩明细是否存在
                if (ObjectUtils.isNotEmpty(scoreList.getScoreListDetails())) {
                    updateFinalExamScoreDetails(scoreList, scoreBD);
                } else {
                    log.debug("在校成绩详情为空，无需更新: studentId={}", scoreList.getStudentId());
                }
            } else {
                log.debug("其他类型成绩无需特殊处理: type={}, studentId={}",
                        scoreList.getType(), scoreList.getStudentId());
            }
        } catch (Exception e) {
            log.error("更新成绩数据失败: {}", e.getMessage(), e);
            throw new BaseException("更新成绩数据失败: " + e.getMessage());
        }
    }

    /**
     * 更新在校成绩详情
     *
     * @param scoreList 新的成绩列表对象
     * @param existingScore 已存在的成绩列表对象
     */
    private void updateFinalExamScoreDetails(ScoreList scoreList, ScoreList existingScore) {
        //更新主表成绩
        existingScore.setExamScore(scoreList.getExamScore());
        existingScore.setStudentTestId(scoreList.getStudentTestId());
        existingScore.setUpdateTime(new Date());
        baseMapper.updateById(existingScore);

        ScoreListDetails detail = new LambdaQueryChainWrapper<>(scoreListDetailsMapper)
                .eq(ScoreListDetails::getScoreListId, existingScore.getId())
                .eq(ScoreListDetails::getCourseId, scoreList.getCourseId())
                .eq(ScoreListDetails::getGradeTerm, scoreList.getScoreListDetails().getGradeTerm())
                .last("limit 1")
                .one();

        if (detail == null) {
            // 成绩详情不存在，创建新记录
            log.debug("在校成绩详情不存在，创建新记录: studentId={}, courseId={}",
                    scoreList.getStudentId(), scoreList.getCourseId());

            ScoreListDetails newDetail = scoreList.getScoreListDetails();
            newDetail.setScoreListId(existingScore.getId());
            newDetail.setCreateTime(new Date());
            scoreListDetailsMapper.insert(newDetail);
        } else {
            // 成绩详情存在，更新记录
            log.debug("更新在校成绩详情: studentId={}, courseId={}, detailId={}",
                    scoreList.getStudentId(), scoreList.getCourseId(), detail.getId());

            ScoreListDetails updatedDetail = scoreList.getScoreListDetails();
            updatedDetail.setId(detail.getId());
            updatedDetail.setUpdateTime(new Date());
            scoreListDetailsMapper.updateById(updatedDetail);
        }
    }

    private void checkOfflineResults(ScoreListDetails scoreListDetails,ScoreList scoreList) {
        if (scoreListDetails == null || scoreList == null) {
            log.warn("成绩详情或成绩列表为空，无法检查线下成绩");
            return;
        }

        Long gradeId = scoreList.getGradeId();
        Long specialtyId = scoreList.getSpecialtyId();
        Long courseId = scoreList.getCourseId();
        String orgCode = scoreList.getOrgCode();
        Integer gradeTerm = scoreList.getGradeTermId();
        Long levelId = scoreList.getLevelId();

        if (gradeId == null || specialtyId == null || courseId == null || orgCode == null) {
            log.info("成绩信息不完整，无法查询线下成绩比例: gradeId={}, specialtyId={}, courseId={}, orgCode={}, gradeTerm={}, levelId={}",
                    gradeId, specialtyId, courseId, orgCode, gradeTerm, levelId);
            return;
        }

        try {
            SpecialtyPlanCourse course = baseMapper.selectPlanCourseRatio(orgCode, gradeId, specialtyId, courseId, gradeTerm, levelId);
            if (course != null) {
                log.info("找到课程线下成绩比例配置: courseId={}, orgCode={}", courseId, orgCode);
                // 设置线上比例
                scoreListDetails.setOnlineDiscussion(course.getOnlineDiscussion());
                scoreListDetails.setOnlineComprehensiveScore(course.getOnlineComprehensiveScore());

                // 设置线下比例
                scoreListDetails.setOfflineDiscussion(course.getOfflineDiscussion());
                scoreListDetails.setOfflineTeaching(course.getOfflineTeaching());
                scoreListDetails.setOfflineOther(course.getOfflineOther());
                scoreListDetails.setOfflineExamResult(course.getOfflineExamResult());
                scoreListDetails.setOfflineComprehensiveScore(course.getOfflineComprehensiveScore());
            } else {
                log.info("未找到课程线下成绩比例配置: courseId={}, orgCode={}", courseId, orgCode);
            }
        } catch (Exception e) {
            log.error("查询线下成绩比例出错: {}", e.getMessage(), e);
        }
    }

    public void updateZkScore(ScoreListDetails scoreListDetails, ScoreListTypeEnum type, BigDecimal score) {
        if (score == null) score = BigDecimal.ZERO;
        if (type == ScoreListTypeEnum.UNIFIED_EXAM) {
            if (scoreListDetails.getUnifiedScoreRatio() == null) {
                scoreListDetails.setCompositeScore(BigDecimal.ZERO);
            } else {
                if (scoreListDetails.getUnifiedScore() != null) {
                    scoreListDetails.setCompositeScore(scoreListDetails.getUnifiedScoreRatio().multiply(scoreListDetails.getUnifiedScore()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN)
                            .add(scoreListDetails.getUnifiedScoreRatio().multiply(score).divide(new BigDecimal("100"), 2, RoundingMode.DOWN)));
                } else {
                    scoreListDetails.setCompositeScore(scoreListDetails.getUnifiedScoreRatio().multiply(score).divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
                }
            }
            scoreListDetails.setComputerScore(scoreListDetails.getCompositeScore().setScale(0, RoundingMode.DOWN));
        }
        if (type == ScoreListTypeEnum.COMPUTER_EXAM) {
            if (scoreListDetails.getInternetPlan() == null
                    || scoreListDetails.getInternetPlanRatio() == null
                    || scoreListDetails.getWorkScore() == null
                    || scoreListDetails.getWorkScoreRatio() == null
                    || scoreListDetails.getComputerScoreRatio() == null) {
                scoreListDetails.setCompositeScore(BigDecimal.ZERO);
            }
            if (scoreListDetails.getInternetPlan() == null) {
                scoreListDetails.setInternetPlan(BigDecimal.ZERO);
            }
            if (scoreListDetails.getInternetPlan().compareTo(Optional.ofNullable(scoreListDetails.getCourseFullMarks()).orElse(BigDecimal.ZERO)) < 0) {
                BigDecimal number1 = ObjectUtils.isNotEmpty(scoreListDetails.getInternetPlanRatio()) ? scoreListDetails.getInternetPlanRatio().multiply(scoreListDetails.getInternetPlan()).divide(new BigDecimal("100")) : BigDecimal.ZERO;
                BigDecimal number2 = ObjectUtils.isNotEmpty(scoreListDetails.getWorkScoreRatio()) ? scoreListDetails.getWorkScoreRatio().multiply(scoreListDetails.getWorkScore()).divide(new BigDecimal("100")) : BigDecimal.ZERO;
                BigDecimal number3 = ObjectUtils.isNotEmpty(scoreListDetails.getComputerScoreRatio()) ? scoreListDetails.getComputerScoreRatio().multiply(score).divide(new BigDecimal("100")) : new BigDecimal(100).multiply(score).divide(new BigDecimal("100")) ;
                BigDecimal compositeScore = number1.add(number2).add(number3);
                scoreListDetails.setCompositeScore(compositeScore.setScale(0, RoundingMode.DOWN));
               /* scoreListDetails.setCompositeScore(scoreListDetails.getInternetPlanRatio().multiply(scoreListDetails.getInternetPlan()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN)
                        .add(scoreListDetails.getWorkScore().multiply(scoreListDetails.getWorkScoreRatio()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN))
                        .add(scoreListDetails.getComputerScoreRatio().multiply(score).divide(new BigDecimal("100"), 2, RoundingMode.DOWN)));*/
            } else {
                BigDecimal number1 = ObjectUtils.isNotEmpty(scoreListDetails.getInternetPlanRatio()) ? scoreListDetails.getInternetPlanRatio().multiply(scoreListDetails.getInternetPlan()).divide(new BigDecimal("100")) : BigDecimal.ZERO;
                BigDecimal number2 = ObjectUtils.isNotEmpty(scoreListDetails.getWorkScoreRatio()) ? scoreListDetails.getWorkScoreRatio().multiply(scoreListDetails.getWorkScore()).divide(new BigDecimal("100")) : BigDecimal.ZERO;
                BigDecimal number3 = ObjectUtils.isNotEmpty(scoreListDetails.getComputerScoreRatio()) ? scoreListDetails.getComputerScoreRatio().multiply(score).divide(new BigDecimal("100")) : new BigDecimal(100).multiply(score).divide(new BigDecimal("100")) ;
                BigDecimal compositeScore = number1.add(number2).add(number3);
                scoreListDetails.setCompositeScore(compositeScore.setScale(0, RoundingMode.DOWN));
            }
            scoreListDetails.setComputerScore(scoreListDetails.getComputerScore() != null ? scoreListDetails.getComputerScore().setScale(0, RoundingMode.DOWN) :BigDecimal.ZERO);
        }
        scoreListDetails.setPassScore(new BigDecimal("60"));
        scoreListDetails.setIsPass(scoreListDetails.getCompositeScore().compareTo(scoreListDetails.getPassScore()) >= 0 ? 1 : 0);
    }

    public void updateCjScore(ScoreListDetails scoreListDetails, BigDecimal examScore) {
        if (examScore == null) examScore = BigDecimal.ZERO;
        BigDecimal score = BigDecimal.ZERO;
        if (scoreListDetails.getStudyPlan() != null && scoreListDetails.getStudyPlanRatio() != null && scoreListDetails.getStudyPlanRatio().compareTo(BigDecimal.ZERO) > 0) {
            score = score.add(scoreListDetails.getStudyPlan().multiply(scoreListDetails.getStudyPlanRatio()).divide(new BigDecimal("100")));
        }
        if (scoreListDetails.getInformationPlan() != null && scoreListDetails.getInformationRatio() != null && scoreListDetails.getInformationRatio().compareTo(BigDecimal.ZERO) > 0) {
            score = score.add(scoreListDetails.getInformationPlan().multiply(scoreListDetails.getInformationRatio()).divide(new BigDecimal("100")));
        }
        if (scoreListDetails.getJobScore() != null && scoreListDetails.getJobScoreRatio() != null && scoreListDetails.getJobScoreRatio().compareTo(BigDecimal.ZERO) > 0) {
            score = score.add(scoreListDetails.getJobScore().multiply(scoreListDetails.getJobScoreRatio()).divide(new BigDecimal("100")));
        }
        if (scoreListDetails.getExerciseScore() != null && scoreListDetails.getExerciseScoreRatio() != null && scoreListDetails.getExerciseScoreRatio().compareTo(BigDecimal.ZERO) > 0) {
            score = score.add(scoreListDetails.getExerciseScore().multiply(scoreListDetails.getExerciseScoreRatio()).divide(new BigDecimal("100")));
        }
        if (scoreListDetails.getExamScore() != null && scoreListDetails.getExamScoreRatio().compareTo(BigDecimal.ZERO) > 0) {
            score = score.add(examScore.multiply(scoreListDetails.getExamScoreRatio()).divide(new BigDecimal("100")));
        }
        score = score.setScale(0, RoundingMode.DOWN);
        scoreListDetails.setCompositeScore(score);
        scoreListDetails.setPassScore(new BigDecimal("60"));
        scoreListDetails.setIsPass(score.compareTo(scoreListDetails.getPassScore()) >= 0 ? 1 : 0);
        log.info("处理综合分数:{}", score);
    }

    /**
     * 成绩模板下载
     *
     * @param request
     * @param response
     */
    @Override
    public void downExample(HttpServletRequest request, HttpServletResponse response, Long plId, ScoreListTypeEnum type) {
        ConditionAssert.isTrue(plId == null, "请选择平台");
        Platform platform = platformManager.getById(plId);
        ConditionAssert.isTrue(platform == null, "平台不存在");
        switch (type) {
            case FINAL_EXAM -> finalExam(platform.getPlatformName(), response);
            case UNIFIED_EXAM -> unifiedExam(platform.getPlatformName(), response);
            case COMPUTER_EXAM -> computerExam(platform.getPlatformName(), response);
            default -> throw new BaseException("模块不存在");
        }
    }

    /**
     * 删除录入成绩
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteEnterScore(Long id) {
        // 主表
        ScoreList scoreList = baseMapper.selectById(id);
        ConditionAssert.isTrue(scoreList == null, "数据不存在");
        // 详情
        List<ScoreListDetails> list = scoreListDetailsMapper.getInfoByScoreListId(id);
        if (ObjectUtils.isNotEmpty(list)) {
            List<ScoreListDetails> newList = enterScoreDispose(list, scoreList.getType());
            if (ObjectUtils.isNotEmpty(newList)) {
                scoreListDetailsService.updateBatchById(newList);
            }
        }
        return true;
    }

    /**
     * 批量删除录入成绩
     *
     * @param ids
     * @return
     */
    @Override
    public Boolean deleteEnterScores(List<Long> ids) {
        for (Long id : ids) {
            // 主表
            ScoreList scoreList = baseMapper.selectById(id);
            ConditionAssert.isTrue(scoreList == null, "数据不存在");
            // 详情
            List<ScoreListDetails> list = scoreListDetailsMapper.getInfoByScoreListId(id);
            if (ObjectUtils.isNotEmpty(list)) {
                List<ScoreListDetails> newList = enterScoreDispose(list, scoreList.getType());
                if (ObjectUtils.isNotEmpty(newList)) {
                    scoreListDetailsService.updateBatchByIds(newList);

                }
            }
        }
        return true;
    }

    @Override
    public List<ScoreListExport> downExcelComputerExam(ScoreListQuery query) {
        TimeInterval timer = new TimeInterval();
        List<ScoreListExport> exports = new ArrayList<>();
        List<Long> ids = baseMapper.selectRelationshipQuery(query);
        List<ScoreListVo> scoreListVos = baseMapper.selectQuery(ids,query.getType());
        log.info("查询成绩列表耗时：{}", timer.interval());
        timer.intervalRestart();
        if (CollectionUtils.isEmpty(scoreListVos)) {
            return exports;
        }
        nameManager.fillCreateName(scoreListVos, true);
        nameManager.fillPlatName(scoreListVos, "plId", "platName");
        nameManager.fillOrgName(scoreListVos, "orgCode", "schoolName");
        nameManager.fillOrgName(scoreListVos, "orgCode", "orgName");
        scoreListVos.forEach(scoreVo -> {
            ScoreListDetailsQuery detailsQuery = new ScoreListDetailsQuery();
            detailsQuery.setScoreListId(scoreVo.getId());
            detailsQuery.setPage(1L);
            detailsQuery.setSize(9999L);
            PageInfo<ScoreListDetailsVo> pageInfo = scoreListDetailsService.pageList(detailsQuery);
            List<ScoreListDetailsVo> list = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(detail -> {
                    ScoreListExport export = JSONObject.parseObject(JSON.toJSONString(scoreVo), ScoreListExport.class);
                    BeanUtils.copyProperties(detail, export);
                    export.setIsPass(detail.getIsPass() == 0 ? "否" : "是");
                    export.setIsPublish(detail.getIsPublish() == 0 ? "待发布" : "已发布");

                    if (detail.getLineComputerScore() != null && detail.getComputerScore() != null) {
                        export.setLineOrComputerScore(detail.getLineComputerScore() + "/" + detail.getComputerScore());
                    }else if (detail.getLineComputerScore() != null) {
                        export.setLineOrComputerScore(String.valueOf(detail.getLineComputerScore().doubleValue()));
                    }else if (detail.getComputerScore() != null) {
                        export.setLineOrComputerScore(String.valueOf(detail.getComputerScore().doubleValue()));
                    }
                    exports.add(export);
                });
            }else {
                ScoreListExport export = JSONObject.parseObject(JSON.toJSONString(scoreVo), ScoreListExport.class);
                exports.add(export);
            }
        });

        log.info("查询成绩列表耗时：{}", timer.interval());
        return exports;
    }

    /**
     * 导出在校成绩表单
     *
     * @param query
     * @return
     */
    @Override
    public List<ScoreListFinalExamExport> downExcelFinalExam(ScoreListQuery query) {
        List<ScoreListFinalExamExport> exports = new ArrayList<>();
        TimeInterval timer = new TimeInterval();
        List<Long> ids = baseMapper.selectRelationshipQuery(query);
        List<ScoreListVo> scoreListVos = baseMapper.selectQuery(ids,query.getType());
        log.info("查询成绩列表耗时：{}", timer.interval());
        timer.intervalRestart();
        if (CollectionUtils.isEmpty(scoreListVos)) {
            return exports;
        }
        nameManager.fillCreateName(scoreListVos, true);
        nameManager.fillPlatName(scoreListVos, "plId", "platName");
        nameManager.fillOrgName(scoreListVos, "orgCode", "schoolName");
        nameManager.fillOrgName(scoreListVos, "orgCode", "orgName");

        scoreListVos.forEach(scoreVo -> {
            ScoreListDetailsQuery detailsQuery = new ScoreListDetailsQuery();
            detailsQuery.setScoreListId(scoreVo.getId());
            detailsQuery.setPage(1L);
            detailsQuery.setSize(9999L);
            PageInfo<ScoreListDetailsVo> pageInfo = scoreListDetailsService.pageList(detailsQuery);
            List<ScoreListDetailsVo> list = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(detail -> {
                    ScoreListFinalExamExport export = JSONObject.parseObject(JSON.toJSONString(scoreVo), ScoreListFinalExamExport.class);
                    BeanUtils.copyProperties(detail, export);
                    //0-否，1-是
                    export.setIsDegreeCourse(detail.getIsDegreeCourse() == null || detail.getIsDegreeCourse() == 0 ? "否" : "是");
                    export.setIsPass(detail.getIsPass() == null || detail.getIsPass() == 0 ? "否" : "是");
                    export.setIsPublish(detail.getIsPublish() == null || detail.getIsPublish() == 0 ? "待发布" : "已发布");
                    export.setDataSources(scoreVo.getDataSources());
                    export.setGradeTerm("第" + detail.getGradeTerm() + "学期");
                    exports.add(export);
                });
            }else {
                ScoreListFinalExamExport export = JSONObject.parseObject(JSON.toJSONString(scoreVo), ScoreListFinalExamExport.class);
                export.setGradeTerm("第" + scoreVo.getGradeTermId() + "学期");
                exports.add(export);
            }
        });
        log.info("查询成绩列表耗时1：{}", timer.interval());
        return exports;
    }

    @Override
    public List<GraduationRuleScoreVO> getGraduationRuleScore(GraduationRuleDTO dto) {
        return baseMapper.getGraduationRuleScore(dto);
    }

    /**
     * 考试参考情况图统计
     *
     * @param query
     * @return
     */
    @Override
    public PageInfo<ExamPeopleIconExport> examPeopleIcon(ExaminationStatisticsQuery query) {
        PageInfo<ExamPeopleIconExport> pageInfo = null;
        switch (query.getType()) {
            case FINAL_EXAM -> {
                pageInfo = PageInfo.of(query, () -> baseMapper.selectFinalExamPeopleIcon(query));

                switch (query.getExamType()) {
                    // 正考
                    case 1 -> {
                        pageInfo.getList().forEach(item -> {
                            // EnrollExamPeopleQuery enrollExamPeopleQuery = new EnrollExamPeopleQuery();
                            // BeanUtils.copyProperties(item, enrollExamPeopleQuery);
                            // // 应考人数
                            // long oughtNumber = Ret.feignResult(enrollStudentAdultFeign.getStudentSum(enrollExamPeopleQuery));
                            // List<Long> courseIds = Arrays.stream(item.getCourseInfo().split(",")).map(Long::parseLong).collect(Collectors.toList());
                            // item.setOughtNumber(oughtNumber * courseIds.size());
                            // // 集体条件
                            // List<StudentConditionDTO> collectivityList = baseMapper.getFinalCollectivityStudent(item.getPlId(), item.getOrgCode());
                            // // 个人条件
                            // List<StudentConditionDTO> personageList = baseMapper.getFinalPersonageStudent(item.getPlId(), item.getOrgCode());
                            //
                            // // 条件排除人数
                            // long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), courseIds, collectivityList, personageList);
                            // item.setOughtNumber(item.getOughtNumber() - studentCount);

                            createZKNumber(item);
                            setPeopleIconRate(item);
                        });
                    }
                    // 补考
                    case 2 -> {
                        pageInfo.getList().forEach(item -> {
                            // StudentTestExamPeopleQuery examPeopleQuery = new StudentTestExamPeopleQuery();
                            // BeanUtils.copyProperties(item, examPeopleQuery);
                            // examPeopleQuery.setEndDate(query.getExamStartTime());
                            // List<Long> courseIds = Arrays.stream(item.getCourseInfo().split(",")).map(Long::parseLong).collect(Collectors.toList());
                            // examPeopleQuery.setCourseIds(courseIds);
                            // // 应考人数
                            // long oughtNumber = Ret.feignResult(studentTestFeign.getExamPeopleSum(examPeopleQuery));
                            // item.setOughtNumber(oughtNumber);

                            createBKNumber(item);
                            setPeopleIconRate(item);
                        });
                    }
                    // 缓考、重修
                    default -> {
                        pageInfo.getList().forEach(item -> {
                            createHKOrCXNumber(item, query.getExamType());
                            setPeopleIconRate(item);
                        });
                    }
                }
            }
            case ALONE_EXAM -> {
                pageInfo = PageInfo.of(query, () -> baseMapper.selectAloneExamPeopleIcon(query));
                pageInfo.getList().forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getAloneCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getAlonePersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    setPeopleIconRate(item);
                });
            }
            case UNIFIED_EXAM -> {
                pageInfo = PageInfo.of(query, () -> baseMapper.selectUnifiedExamPeopleIcon(query));
                pageInfo.getList().forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getUnifiedCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getUnifiedPersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    // StudentTestTypeEnum 枚举，期末平时4次、期中、期末
//                    item.setOughtNumber(item.getOughtNumber() * 6);
                    setPeopleIconRate(item);
                });
            }
            case COMPUTER_EXAM -> {
                pageInfo = PageInfo.of(query, () -> baseMapper.selectComputerExamPeopleIcon(query));
                pageInfo.getList().forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getComputerCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getComputerPersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    setPeopleIconRate(item);
                });
            }
            default -> throw new BaseException("类型错误");
        }

        nameManager.fillPlatName(pageInfo.getList(), "plId", "plName");
        nameManager.fillOrgName(pageInfo.getList(), "orgCode", "orgName");
        return pageInfo;
    }

    private void createZKNumber(ExamPeopleIconExport item) {
        PlanExamRuleOrgTermDTO planExamRuleOrgTermDTO = getRuleOrgTermDTO(item);
        if (planExamRuleOrgTermDTO != null && !planExamRuleOrgTermDTO.getIsSetZk()) {
            return;
        }
        //应考
        Long oughtNumber = 0L;
        // 查询该组织下的所有学员
        Map<String, List<EnrollStudentAdult>> adultMap = getStringListMap(item);

        //查询该组织下的绑定试卷
        Map<String, List<PlanBindTestPaperVo>> testPaperMap = getListMap(item);
        for (Map.Entry<String, List<PlanBindTestPaperVo>> entry : testPaperMap.entrySet()) {
            String key = entry.getKey();
            // 对应年级、层次、专业下的试卷
            List<PlanBindTestPaperVo> value = entry.getValue();

            // 获取对应年级、层次、专业下的学员个数
            int studentNum = adultMap.get(key) != null ? adultMap.get(key).size() : 0;

            for (PlanBindTestPaperVo planBindTestPaperVo : value) {
                List<GradeTerm> gradeTerms = gradeTermMap.get(planBindTestPaperVo.getId() + "gradeTerm");
                if (CollectionUtils.isEmpty(gradeTerms)) {
                    gradeTerms = gradeTermService.semesterSelect(planBindTestPaperVo.getId());
                    gradeTermMap.put(planBindTestPaperVo.getId() + "gradeTerm", gradeTerms);
                }
                Map<String, Long> termMap = gradeTerms.stream().collect(Collectors.toMap(GradeTerm::getTermNum, GradeTerm::getId));

                List<SpecialtyPlanCourse> courseList = getSpecialtyPlanCourses(planBindTestPaperVo, termMap.get(String.valueOf(item.getGradeTerm())));
                int totalZkBindTestCount = courseList.stream()
                        .mapToInt(SpecialtyPlanCourse::getZkBindTestCount)
                        .sum();

                oughtNumber += ((long)studentNum * totalZkBindTestCount);
            }
        }
        item.setOughtNumber(oughtNumber);
        //实考
        StudentMyExamQuery examQuery = new StudentMyExamQuery();
        examQuery.setPlId(item.getPlId());
        examQuery.setOrgCode(item.getOrgCode());
        examQuery.setTestType(StudentTestTypeEnum.FINAL_EXAM_ZK);
        Long realityNumber = baseMapper.getStudentTestNumber(examQuery);
        item.setRealityNumber(realityNumber);
        //缺考
        item.setMissingNumber(oughtNumber - realityNumber);
    }

    private void createBKNumber(ExamPeopleIconExport item) {
        PlanExamRuleOrgTermDTO planExamRuleOrgTermDTO = getRuleOrgTermDTO(item);
        if (planExamRuleOrgTermDTO != null && !planExamRuleOrgTermDTO.getIsSetBk()) {
            return;
        }

        //应考
        StudentMyExamQuery query = new StudentMyExamQuery();
        query.setPlId(item.getPlId());
        query.setOrgCode(item.getOrgCode());
        Long oughtNumber = baseMapper.getTestBKNumber(query);
        item.setOughtNumber(oughtNumber);

        //实考
        if (oughtNumber != null) {
            StudentMyExamQuery examQuery = new StudentMyExamQuery();
            examQuery.setPlId(item.getPlId());
            examQuery.setOrgCode(item.getOrgCode());
            examQuery.setTestType(StudentTestTypeEnum.FINAL_EXAM_BK);
            Long realityNumber = baseMapper.getStudentTestNumber(examQuery);
            item.setRealityNumber(realityNumber != null ? realityNumber : 0);
            //缺考
            item.setMissingNumber(item.getOughtNumber() - item.getRealityNumber());
        }
    }
    private ExamPeopleIconExport createHKOrCXNumber(ExamPeopleIconExport item, Integer examType) {
        PlanExamRuleOrgTermDTO planExamRuleOrgTermDTO = getRuleOrgTermDTO(item);
        if (examType == 3 && (planExamRuleOrgTermDTO != null && !planExamRuleOrgTermDTO.getIsSetHk())) {
            return null;
        }else {
            if (planExamRuleOrgTermDTO != null && !planExamRuleOrgTermDTO.getIsSetCk()) {
                return null;
            }
        }
        //应考
        Long oughtNumber = 0L;
        // 查询该组织下的所有学员
        Map<String, List<EnrollStudentAdult>> adultMap = getStringListMap(item);

        // 培养计划绑定的试卷
        Map<String, List<PlanBindTestPaperVo>> testPaperMap = getListMap(item);
        for (Map.Entry<String, List<PlanBindTestPaperVo>> entry : testPaperMap.entrySet()) {
            String key = entry.getKey();
            // 对应年级、层次、专业下的试卷
            List<PlanBindTestPaperVo> value = entry.getValue();

            // 获取对应年级、层次、专业下的学员个数
            int studentNum = adultMap.get(key) != null ? adultMap.get(key).size() : 0;

            for (PlanBindTestPaperVo planBindTestPaperVo : value) {
                List<GradeTerm> gradeTerms = gradeTermMap.get(planBindTestPaperVo.getId() + "gradeTerm");
                if (CollectionUtils.isEmpty(gradeTerms)) {
                    gradeTerms = gradeTermService.semesterSelect(planBindTestPaperVo.getId());
                    gradeTermMap.put(planBindTestPaperVo.getId() + "gradeTerm", gradeTerms);
                }
                Map<String, Long> termMap = gradeTerms.stream().collect(Collectors.toMap(GradeTerm::getTermNum, GradeTerm::getId));

                List<SpecialtyPlanCourse> courseList = getSpecialtyPlanCourses(planBindTestPaperVo, termMap.get(String.valueOf(item.getGradeTerm())));

                int totalTestCount = 0;
                if (examType == 3) {
                    totalTestCount = courseList.stream()
                            .mapToInt(SpecialtyPlanCourse::getHkBindTestCount)
                            .sum();
                }else {
                    totalTestCount = courseList.stream()
                            .mapToInt(SpecialtyPlanCourse::getCkBindTestCount)
                            .sum();
                }
                oughtNumber += ((long)studentNum * totalTestCount);
            }
        }
        item.setOughtNumber(oughtNumber);
        //实考
        StudentMyExamQuery examQuery = new StudentMyExamQuery();
        examQuery.setPlId(item.getPlId());
        examQuery.setOrgCode(item.getOrgCode());
        if (examType == 3) {
            examQuery.setTestType(StudentTestTypeEnum.FINAL_EXAM_HK);
        }else {
            examQuery.setTestType(StudentTestTypeEnum.FINAL_EXAM_CX);
        }
        Long realityNumber = baseMapper.getStudentTestNumber(examQuery);
        item.setRealityNumber(realityNumber);
        //缺考
        item.setMissingNumber(oughtNumber - realityNumber);
        return item;
    }

    private PlanExamRuleOrgTermDTO getRuleOrgTermDTO(ExamPeopleIconExport item) {
        List<PlanExamRuleOrgTermDTO> planExamRuleOrgTermList = ruleOrgTermMap.get(item.getGradeId() + item.getPlId() + item.getOrgCode() + "_PlanExamRuleOrgTerm");
        if (CollectionUtils.isEmpty(planExamRuleOrgTermList)) {
            planExamRuleOrgTermList = specialtyPlanFeign.getPlanExamRuleOrgTermList(item.getGradeId(), item.getPlId(), item.getOrgCode());
            ruleOrgTermMap.put(item.getGradeId() + item.getPlId() + item.getOrgCode() + "_PlanExamRuleOrgTerm", planExamRuleOrgTermList);
        }
        Map<Integer, PlanExamRuleOrgTermDTO> ruleOrgTermDTOMap = planExamRuleOrgTermList.stream().collect(Collectors.toMap(PlanExamRuleOrgTermDTO::getTerm, term -> term, (existing, replacement) -> existing));
        return ruleOrgTermDTOMap.get(item.getGradeTerm());
    }

    @NotNull
    private Map<String, List<PlanBindTestPaperVo>> getListMap(ExamPeopleIconExport item) {
        List<PlanBindTestPaperVo> planBindTestPaperList = testPaperVoMap.get(item.getPlId() + item.getOrgCode() + "testPaper");
        if (CollectionUtils.isEmpty(planBindTestPaperList)) {
            SpecialtyPlanSelectQuery planSelectQuery = new SpecialtyPlanSelectQuery();
            planSelectQuery.setPlId(item.getPlId());
            planSelectQuery.setOrgCode(item.getOrgCode());
            planBindTestPaperList = specialtyPlanFeign.getPlanBindTestPaperList(planSelectQuery);
            testPaperVoMap.put(item.getPlId() + item.getOrgCode() + "testPaper", planBindTestPaperList);
        }
        Map<String, List<PlanBindTestPaperVo>> testPaperMap = planBindTestPaperList.stream()
                .collect(Collectors.groupingBy(e -> e.getGradeId() + "-" + e.getLevelId() + "-" + e.getSpecialtyId()));
        return testPaperMap;
    }

    @NotNull
    private Map<String, List<EnrollStudentAdult>> getStringListMap(ExamPeopleIconExport item) {
        List<EnrollStudentAdult> enrollStudentAdultList = stringListMap.get(item.getPlId() + item.getOrgCode() + "student");

        if (CollectionUtils.isEmpty(enrollStudentAdultList)) {
            EnrollStudentAdultQuery adultQuery = new EnrollStudentAdultQuery();
            adultQuery.setPlId(item.getPlId());
            adultQuery.setOneOrgCode(item.getOrgCode());
            enrollStudentAdultList = enrollStudentAdultFeign.getEnrollStudentAdults(adultQuery);
            stringListMap.put(item.getPlId() + item.getOrgCode() + "student", enrollStudentAdultList);
        }
        // 统计对应年级、层次、专业下的学员
        return enrollStudentAdultList.stream().collect(Collectors.groupingBy(e -> e.getGradeId() + "-" + e.getLevelId() + "-" + e.getSpecialtyId()));
    }

    private List<SpecialtyPlanCourse> getSpecialtyPlanCourses(PlanBindTestPaperVo planBindTestPaperVo, Long term) {

        List<SpecialtyPlanCourse> specialtyPlanCourses = null;
        if (term != null) {
            specialtyPlanCourses = planCourseMap.get(planBindTestPaperVo.getId() + term + "SpecialtyPlanCourse");
        }else {
            specialtyPlanCourses = planCourseMap.get(planBindTestPaperVo.getId() + "SpecialtyPlanCourse");
        }
        if (CollectionUtils.isEmpty(specialtyPlanCourses)) {
            SpecialtyPlanCourseQuery courseQuery = new SpecialtyPlanCourseQuery();
            courseQuery.setPlanId(planBindTestPaperVo.getId());
            courseQuery.setSemester(term);
            specialtyPlanCourses = specialtyPlanFeign.getPlanBindTestCourseList(courseQuery);
            if (term != null) {
                planCourseMap.put(planBindTestPaperVo.getId() + term + "SpecialtyPlanCourse", specialtyPlanCourses);
            }else {
                planCourseMap.put(planBindTestPaperVo.getId() + "SpecialtyPlanCourse", specialtyPlanCourses);
            }
        }
        return specialtyPlanCourses;
    }

    /**
     * 条件学员排除
     *
     * @param collectivityList
     * @param personageList
     * @return
     */
    private long conditionDispose(Long plId, String orgCode, List<Long> courseIds, List<StudentConditionDTO> collectivityList, List<StudentConditionDTO> personageList) {
        List<Long> idsToRemove = personageList.stream()
                .map(StudentConditionDTO::getStudentId)
                .collect(Collectors.toList());

        if (ObjectUtils.isNotEmpty(idsToRemove)) {
            // 移除符合条件的学生
            collectivityList.removeIf(obj -> idsToRemove.contains(obj.getStudentId()));
            // 合并
            collectivityList.addAll(personageList);
        }
        // 移除课程、练习、学习次数小于0的
        collectivityList.removeIf(obj -> obj.getCourseRate().compareTo(BigDecimal.ZERO) <= 0 && obj.getZyCount() <= 0 && obj.getLxCount() <= 0);
        if (ObjectUtils.isNotEmpty(collectivityList)) {
            // 不符合条件学生数量
            List<CheckExamPlanDTO> dto = new ArrayList<>();
            collectivityList.forEach(obj -> {
                CheckExamPlanDTO cepd = new CheckExamPlanDTO();
                cepd.setPlatformId(plId);
                cepd.setOrgCode(orgCode);
                cepd.setCourseIds(courseIds);
                cepd.setUserId(obj.getStudentId());
                cepd.setCourseRate(obj.getCourseRate());
                cepd.setZyCount(obj.getZyCount());
                cepd.setLxCount(obj.getLxCount());
                dto.add(cepd);
            });
            long studentCount = Ret.feignResult(studentTestFeign.getNoCheckExamCondition(dto));
            return studentCount;
        }
        return 0;
    }

    /**
     * 考试参考情况图比例计算
     *
     * @param item
     */
    private void setPeopleIconRate(ExamPeopleIconExport item) {
        if (item.getOughtNumber() <= 0) {
            item.setOughtNumber(0L);
            item.setRealityNumber(0L);
            item.setMissingNumber(0L);
            item.setReferenceRate("0%");
            item.setMissingRate("0%");
        } else {
            item.setMissingNumber(item.getOughtNumber() - item.getRealityNumber());

            // 比例
            BigDecimal referenceRate = item.getRealityNumber() <= 0 ? BigDecimal.ZERO : new BigDecimal(item.getRealityNumber()).divide(new BigDecimal(item.getOughtNumber()), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            ;
            BigDecimal missingRate = item.getMissingNumber() <= 0 ? BigDecimal.ZERO : new BigDecimal(item.getMissingNumber()).divide(new BigDecimal(item.getOughtNumber()), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            item.setReferenceRate(String.format("%s%%", referenceRate));
            item.setMissingRate(String.format("%s%%", missingRate));
        }
    }

    /**
     * 考试参考情况表统计
     *
     * @param query
     * @return
     */
    @Override
    public PageInfo<ExamPeopleTableExport> examPeopleTable(ExaminationStatisticsQuery query) {
        PageInfo<ExamPeopleTableExport> pageInfo = null;
        switch (query.getType()) {
            case FINAL_EXAM -> {
                // pageInfo = PageInfo.of(query, () -> baseMapper.selectFinalExamPeopleTable(query));
                //按组织设置考试及规则查询正考数据
                pageInfo = PageInfo.of(query, () -> baseMapper.selectFinalExamPeopleTableList(query));
                switch (query.getExamType()) {
                    // 正考
                    case 1 -> {
                        pageInfo.getList().forEach(item -> {
                            // EnrollExamPeopleQuery enrollExamPeopleQuery = new EnrollExamPeopleQuery();
                            // BeanUtils.copyProperties(item, enrollExamPeopleQuery);
                            // // 应考人数
                            // long oughtNumber = Ret.feignResult(enrollStudentAdultFeign.getStudentSum(enrollExamPeopleQuery));
                            // item.setOughtNumber(oughtNumber);
                            //
                            // // 集体条件
                            // List<StudentConditionDTO> collectivityList = baseMapper.getFinalCollectivityStudent(item.getPlId(), item.getOrgCode());
                            // // 个人条件
                            // List<StudentConditionDTO> personageList = baseMapper.getFinalPersonageStudent(item.getPlId(), item.getOrgCode());
                            //
                            // // 条件排除人数
                            // long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                            // item.setOughtNumber(item.getOughtNumber() - studentCount);

                            ExamPeopleIconExport peopleIconExport = JSONObject.parseObject(JSON.toJSONString(item), ExamPeopleIconExport.class);
                            PlanExamRuleOrgTermDTO planExamRuleOrgTermDTO = getRuleOrgTermDTO(peopleIconExport);
                            if (planExamRuleOrgTermDTO != null && !planExamRuleOrgTermDTO.getIsSetZk()) {
                                return;
                            }
                            //查询该年级、专业、层次下的考试应考学员
                            Map<String, List<EnrollStudentAdult>> studentAudltMap = getStringListMap(peopleIconExport);
                            List<EnrollStudentAdult> enrollStudentAdults = studentAudltMap.get(item.getGradeId() + "-" + item.getLevelId() + "-" + item.getSpecialtyId());
                            if (CollectionUtils.isNotEmpty(enrollStudentAdults)) {
                                //应考人数
                                item.setOughtNumber((long)enrollStudentAdults.size());
                            }
                            //实考人数
                            Long realityNumber = getRealityNumber(item, StudentTestTypeEnum.FINAL_EXAM_ZK);
                            item.setRealityNumber(realityNumber);
                            //缺考人数
                            item.setMissingNumber(item.getOughtNumber() - item.getRealityNumber());
                            setPeopleTableRate(item);
                        });
                    }
                    // 补考
                    case 2 -> {
                        pageInfo.getList().forEach(item -> {
                            // StudentTestExamPeopleQuery examPeopleQuery = new StudentTestExamPeopleQuery();
                            // BeanUtils.copyProperties(item, examPeopleQuery);
                            // examPeopleQuery.setEndDate(query.getExamStartTime());
                            // examPeopleQuery.setCourseIds(Collections.singletonList(item.getCourseId()));
                            // // 应考人数
                            // long oughtNumber = Ret.feignResult(studentTestFeign.getExamPeopleSum(examPeopleQuery));
                            // item.setOughtNumber(oughtNumber);
                            ExamPeopleIconExport peopleIconExport = JSONObject.parseObject(JSON.toJSONString(item), ExamPeopleIconExport.class);
                            PlanExamRuleOrgTermDTO planExamRuleOrgTermDTO = getRuleOrgTermDTO(peopleIconExport);
                            if (planExamRuleOrgTermDTO != null && !planExamRuleOrgTermDTO.getIsSetBk()) {
                                return;
                            }
                            //应考
                            // Long oughtNumber = getoughtNumber(item);
                            // item.setOughtNumber(oughtNumber);

                            //实考
                            Long realityNumber = getRealityNumber(item, StudentTestTypeEnum.FINAL_EXAM_BK);
                            item.setRealityNumber(realityNumber);

                            //缺考人数
                            item.setMissingNumber(item.getOughtNumber() - item.getRealityNumber());
                            setPeopleTableRate(item);
                        });
                    }
                    // 缓考、重修
                    default -> {
                        pageInfo.getList().forEach(item -> {
                            ExamPeopleIconExport peopleIconExport = JSONObject.parseObject(JSON.toJSONString(item), ExamPeopleIconExport.class);
                            PlanExamRuleOrgTermDTO planExamRuleOrgTermDTO = getRuleOrgTermDTO(peopleIconExport);
                            if (planExamRuleOrgTermDTO == null) {
                                return;
                            }
                            if (query.getExamType() == 3 && !planExamRuleOrgTermDTO.getIsSetHk()) {
                                return;
                            }
                            if (query.getExamType() == 4 && !planExamRuleOrgTermDTO.getIsSetCk()) {
                                return;
                            }
                            //实考
                            Long realityNumber = getRealityNumber(item, query.getExamType() == 3 ? StudentTestTypeEnum.FINAL_EXAM_HK : StudentTestTypeEnum.FINAL_EXAM_CX);
                            item.setRealityNumber(realityNumber);

                            //缺考人数
                            item.setMissingNumber(item.getOughtNumber() - item.getRealityNumber());
                            setPeopleTableRate(item);
                        });
                    }
                }
            }
            case ALONE_EXAM -> {
                pageInfo = PageInfo.of(query, () -> baseMapper.selectAloneExamPeopleTable(query));
                pageInfo.getList().forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getAloneCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getAlonePersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    setPeopleTableRate(item);
                });
            }
            case UNIFIED_EXAM -> {
                pageInfo = PageInfo.of(query, () -> baseMapper.selectUnifiedExamPeopleTable(query));
                pageInfo.getList().forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getUnifiedCollectivityStudent(item.getPlId(),item.getOrgCode(),item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getUnifiedPersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    // StudentTestTypeEnum 枚举，期末平时4次、期中、期末
                    //item.setOughtNumber(item.getOughtNumber() * 6);
                    setPeopleTableRate(item);
                });
            }
            case COMPUTER_EXAM -> {
                pageInfo = PageInfo.of(query, () -> baseMapper.selectComputerExamPeopleTable(query));
                pageInfo.getList().forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getComputerCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getComputerPersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    setPeopleTableRate(item);
                });
            }
            default -> throw new BaseException("类型错误");
        }
        nameManager.fillPlatName(pageInfo.getList(), "plId", "plName");
        nameManager.fillOrgName(pageInfo.getList(), "orgCode", "orgName");
        return pageInfo;
    }

    private Long getoughtNumber(ExamPeopleTableExport item) {
        StudentMyExamQuery examQuery = new StudentMyExamQuery();
        examQuery.setPlId(item.getPlId());
        examQuery.setOrgCode(item.getOrgCode());
        examQuery.setSpecialtyId(item.getSpecialtyId());
        examQuery.setLevelId(item.getLevelId());
        examQuery.setGradeId(item.getGradeId());
        examQuery.setCourseId(item.getCourseId());
        Long oughtNumber = baseMapper.getTestBKNumber(examQuery);
        return oughtNumber;
    }

    private Long getRealityNumber(ExamPeopleTableExport item, StudentTestTypeEnum testType) {
        StudentMyExamQuery examQuery = new StudentMyExamQuery();
        examQuery.setPlId(item.getPlId());
        examQuery.setOrgCode(item.getOrgCode());
        examQuery.setTestType(testType);
        examQuery.setGradeId(item.getGradeId());
        examQuery.setLevelId(item.getLevelId());
        examQuery.setSpecialtyId(item.getSpecialtyId());
        examQuery.setCourseId(item.getCourseId());
        return baseMapper.getRealityNumber(examQuery);
    }


    /**
     * 考试参考情况图比例计算
     *
     * @param item
     */
    private void setPeopleTableRate(ExamPeopleTableExport item) {
        if (item.getOughtNumber() <= 0) {
            item.setOughtNumber(0L);
            item.setRealityNumber(0L);
            item.setMissingNumber(0L);
            item.setReferenceRate("0%");
            item.setMissingRate("0%");
        } else {
            item.setMissingNumber(item.getOughtNumber() - item.getRealityNumber());

            // 比例
            BigDecimal referenceRate = item.getRealityNumber() <= 0 ? BigDecimal.ZERO : new BigDecimal(item.getRealityNumber()).divide(new BigDecimal(item.getOughtNumber()), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            ;
            BigDecimal missingRate = item.getMissingNumber() <= 0 ? BigDecimal.ZERO : new BigDecimal(item.getMissingNumber()).divide(new BigDecimal(item.getOughtNumber()), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            item.setReferenceRate(String.format("%s%%", referenceRate));
            item.setMissingRate(String.format("%s%%", missingRate));
        }
    }


    /**
     * 考试成绩情况图统计
     *
     * @param query
     * @return
     */
    @Override
    public PageInfo<ExamScoreIconExport> examScoreIcon(ExaminationStatisticsQuery query) {
        PageInfo<ExamScoreIconExport> pageInfo = null;
        switch (query.getType()) {
            case FINAL_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectFinalExamScoreIcon(query));
            case ALONE_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectAloneExamScoreIcon(query));
            case UNIFIED_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectUnifiedExamScoreIcon(query));
            case COMPUTER_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectComputerExamScoreIcon(query));
            default -> throw new BaseException("类型错误");
        }
        if (ObjectUtils.isNotEmpty(pageInfo.getList())) {
            nameManager.fillPlatName(pageInfo.getList(), "plId", "plName");
            nameManager.fillOrgName(pageInfo.getList(), "orgCode", "orgName");
            // 计算比例
            pageInfo.getList().forEach(score -> {
                BigDecimal passNumRatio = score.getPassNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getPassNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                BigDecimal nineNumRatio = score.getNineNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getNineNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                BigDecimal sixtyNumRatio = score.getSixtyNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getSixtyNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                BigDecimal noPassNumRatio = score.getNoPassNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getNoPassNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                score.setPassNumRatio(String.format("%s%%", passNumRatio));
                score.setNineNumRatio(String.format("%s%%", nineNumRatio));
                score.setSixtyNumRatio(String.format("%s%%", sixtyNumRatio));
                score.setNoPassNumRatio(String.format("%s%%", noPassNumRatio));
            });
        }
        return pageInfo;
    }

    @Override
    public PageInfo<ExamScoreTableExport> examScoreTable(ExaminationStatisticsQuery query) {
        PageInfo<ExamScoreTableExport> pageInfo = null;
        switch (query.getType()) {
            case FINAL_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectFinalExamScoreTable(query));
            case ALONE_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectAloneExamScoreTable(query));
            case UNIFIED_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectUnifiedExamScoreTable(query));
            case COMPUTER_EXAM -> pageInfo = PageInfo.of(query, () -> baseMapper.selectComputerExamScoreTable(query));
            default -> throw new BaseException("类型错误");
        }
        if (ObjectUtils.isNotEmpty(pageInfo.getList())) {
            nameManager.fillPlatName(pageInfo.getList(), "plId", "plName");
            nameManager.fillOrgName(pageInfo.getList(), "orgCode", "orgName");
            // 计算比例
            pageInfo.setList(calculateTheScore(pageInfo.getList(), query.getType()));
        }

        return pageInfo;
    }

    /**
     * 考试参考情况表统计导出
     *
     * @param query
     * @param response
     */
    @Override
    public void exportPeopleTable(ExaminationStatisticsQuery query, HttpServletResponse response) {
        List<ExamPeopleTableExport> list = null;
        String fileName = "";
        switch (query.getType()) {
            case FINAL_EXAM -> {
                fileName = "期末考试参考情况";
                list = baseMapper.selectFinalExamPeopleTable(query);

                switch (query.getExamType()) {
                    // 正考
                    case 1 -> {
                        list.forEach(item -> {
                            // EnrollExamPeopleQuery enrollExamPeopleQuery = new EnrollExamPeopleQuery();
                            // BeanUtils.copyProperties(item, enrollExamPeopleQuery);
                            // // 应考人数
                            // long oughtNumber = Ret.feignResult(enrollStudentAdultFeign.getStudentSum(enrollExamPeopleQuery));
                            // item.setOughtNumber(oughtNumber);
                            //
                            // // 集体条件
                            // List<StudentConditionDTO> collectivityList = baseMapper.getFinalCollectivityStudent(item.getPlId(), item.getOrgCode());
                            // // 个人条件
                            // List<StudentConditionDTO> personageList = baseMapper.getFinalPersonageStudent(item.getPlId(), item.getOrgCode());
                            //
                            // // 条件排除人数
                            // long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                            // item.setOughtNumber(item.getOughtNumber() - studentCount);

                            setPeopleTableRate(item);
                        });
                    }
                    // 补考
                    case 2 -> {
                        list.forEach(item -> {
                            // StudentTestExamPeopleQuery examPeopleQuery = new StudentTestExamPeopleQuery();
                            // BeanUtils.copyProperties(item, examPeopleQuery);
                            // examPeopleQuery.setEndDate(query.getExamStartTime());
                            // examPeopleQuery.setCourseIds(Collections.singletonList(item.getCourseId()));
                            // // 应考人数
                            // long oughtNumber = Ret.feignResult(studentTestFeign.getExamPeopleSum(examPeopleQuery));
                            // item.setOughtNumber(oughtNumber);
                            setPeopleTableRate(item);
                        });
                    }
                    // 缓考、重修
                    default -> {
                        list.forEach(item -> {
                            setPeopleTableRate(item);
                        });
                    }
                }
            }
            case ALONE_EXAM -> {
                fileName = "独立考试参考情况";
                list = baseMapper.selectAloneExamPeopleTable(query);
                list.forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getAloneCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getAlonePersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    setPeopleTableRate(item);
                });
            }
            case UNIFIED_EXAM -> {
                fileName = "过程性考试参考情况";
                list = baseMapper.selectUnifiedExamPeopleTable(query);
                list.forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getUnifiedCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getUnifiedPersonageStudent(item.getPlId(),item.getOrgCode(),item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    setPeopleTableRate(item);
                });
            }
            case COMPUTER_EXAM -> {
                fileName = "机考考试参考情况";
                list = baseMapper.selectComputerExamPeopleTable(query);
                list.forEach(item -> {
                    // 集体条件
                    List<StudentConditionDTO> collectivityList = baseMapper.getComputerCollectivityStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());
                    // 个人条件
                    List<StudentConditionDTO> personageList = baseMapper.getComputerPersonageStudent(item.getPlId(), item.getOrgCode(), item.getBatchId());

                    // 条件排除人数
                    long studentCount = conditionDispose(item.getPlId(), item.getOrgCode(), Collections.singletonList(item.getCourseId()), collectivityList, personageList);
                    item.setOughtNumber(item.getOughtNumber() - studentCount);
                    setPeopleTableRate(item);
                });
            }
            default -> throw new BaseException("类型错误");
        }
        nameManager.fillPlatName(list, "plId", "plName");
        nameManager.fillOrgName(list, "orgCode", "orgName");

        try {
            OutputStream outputStream = ExcelUtils.setMimeTypeAndFileName(fileName, response, MimeTypeEnum.XLS);
            EasyExcel.write(outputStream, ExamPeopleTableExport.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new CustomSheetWriteHandler())
                    .registerWriteHandler(ExcelUtils.styleStrategy())
                    .includeColumnFieldNames(query.getExportField())
                    .sheet(fileName).doWrite(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ExcelException("导出失败");
        }
    }

    /**
     * 考试成绩情况表统计导出
     *
     * @param query
     * @param response
     */
    @Override
    public void exportTable(ExaminationStatisticsQuery query, HttpServletResponse response) {
        List<ExamScoreTableExport> list = null;
        String fileName = "";
        switch (query.getType()) {
            case FINAL_EXAM:
                fileName = "期末考试成绩情况";
                list = baseMapper.selectFinalExamScoreTable(query);
                break;
            case ALONE_EXAM:
                fileName = "独立考试成绩情况";
                list = baseMapper.selectAloneExamScoreTable(query);
                break;
            case UNIFIED_EXAM:
                fileName = "过程性考试成绩情况";
                list = baseMapper.selectUnifiedExamScoreTable(query);
                break;
            case COMPUTER_EXAM:
                fileName = "机考考试成绩情况";
                list = baseMapper.selectComputerExamScoreTable(query);
                break;
            default:
                throw new BaseException("类型错误");
        }
        if (ObjectUtils.isNotEmpty(list)) {
            nameManager.fillPlatName(list, "plId", "plName");
            nameManager.fillOrgName(list, "orgCode", "orgName");
            // 计算比例
            list = calculateTheScore(list, query.getType());
        }
        try {
            OutputStream outputStream = ExcelUtils.setMimeTypeAndFileName(fileName, response, MimeTypeEnum.XLS);
            EasyExcel.write(outputStream, ExamScoreTableExport.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new CustomSheetWriteHandler())
                    .registerWriteHandler(ExcelUtils.styleStrategy())
                    .includeColumnFieldNames(query.getExportField())
                    .sheet(fileName).doWrite(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ExcelException("导出失败");
        }
    }

    /**
     * 成绩变更
     *
     * @param query
     * @return
     */
    @Override
    public Boolean changeGrades(ScoreListQuery query) {
        if (query.getId() == null) {
            throw new BaseException("请选择成绩变更数据");
        }
        ScoreList scoreList = new ScoreList();
        scoreList.setId(query.getId());
        scoreList.setChangeGrades(query.getChangeGrades());
        int update = baseMapper.updateById(scoreList);
        return update > 0;
    }

    /**
     * 成绩审核
     *
     * @param query
     * @return
     */
    @Override
    public Boolean auditGrades(ScoreListQuery query) {
        if (CollectionUtils.isEmpty(query.getIds())) {
            throw new BaseException("请选择审核数据");
        }
        List<ScoreList> scoreLists = new ArrayList<>();
        for (Long id : query.getIds()) {
            ScoreList scoreList = new ScoreList();
            scoreList.setId(id);
            scoreList.setAuditStatus(query.getAuditStatus());
            scoreLists.add(scoreList);
        }
        return updateBatchById(scoreLists);
    }

    /**
     * 成绩单PDF下载
     *
     * @param query 模板参数
     * @param response 响应参数
     */
    @Override
    public void transcriptPdf(ScoreListQuery query, HttpServletResponse response) throws IOException {
        List<Long> enrollStudentIds = query.getEnrollStudentIds();
        if (CollectionUtils.isEmpty(enrollStudentIds)) {
            throw new BaseException("请选择下载数据");
        }
        //去重
        List<Long> uniqueStudentIds = enrollStudentIds.stream().distinct().toList();
        query.setEnrollStudentIds(uniqueStudentIds);
        //查询数据信息
        List<StudentAdultStudyVo> scoreList = baseMapper.selectScoreVoList(query);
        if (CollectionUtils.isEmpty(scoreList)) {
            log.error("成绩单数据不存在");
            return;
        }
        //按学员分组
        Map<Long, List<StudentAdultStudyVo>> groupedByStudentId = scoreList.stream()
                .collect(Collectors.groupingBy(StudentAdultStudyVo::getId));
        Set<Map.Entry<Long, List<StudentAdultStudyVo>>> entries = groupedByStudentId.entrySet();

        // 创建临时目录存储PDF文件
        File tempDir = Files.createTempDirectory("transcript_").toFile();
        List<File> pdfFiles = new ArrayList<>();

        try {
            // 为每个学员生成PDF并保存到临时目录
            for (Map.Entry<Long, List<StudentAdultStudyVo>> entry : entries) {
                //数据信息
                List<StudentAdultStudyVo> value = entry.getValue();
                //获取学员基础信息
                StudentAdultStudyVo studentAdultStudyVo = value.get(0);
                ScoreVO.StudentInfo  studentInfo = JSONObject.parseObject(JSONObject.toJSONBytes(studentAdultStudyVo), ScoreVO.StudentInfo.class);

                //通过学期分类
                Map<Integer, List<StudentAdultStudyVo>> termNumCourses = value.stream()
                        .collect(Collectors.groupingBy(StudentAdultStudyVo::getTermNum));

                //学期时间处理
                createtermNumInfo(studentInfo, termNumCourses);
                //成绩处理
                List<ScoreVO.ScoreList> scoreLists = getScoreLists(termNumCourses);

                Map<String, Object> map = new HashMap<>();
                map.put("studentInfo", studentInfo);
                map.put("scoreList", scoreLists);

                String filename = studentInfo.getStudentName() != null ? studentInfo.getStudentName() +  "成绩单.pdf" : "成绩单.pdf";
                // 创建临时PDF文件
                File pdfFile = new File(tempDir, filename);
                try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
                    String html = FreeMarkerHolder.process("transcript.ftl", map);
                    new DocumentTransferWithItext().transfer(html, fos);
                    pdfFiles.add(pdfFile);
                }
            }

            // 设置ZIP响应头
            response.setContentType("application/zip");
            response.setCharacterEncoding("UTF-8");
            String zipFilename = "成绩单.zip";
            try {
                zipFilename = URLEncoder.encode(zipFilename, "UTF-8");
                // 设置响应内容类型为ZIP
                response.setHeader("Content-Disposition", "attachment; filename=" + zipFilename);
            } catch (UnsupportedEncodingException e) {
                log.error("文件名编码错误", e);
                throw new BaseException("生成压缩包失败");
            }

            // 打包ZIP并写入响应
            try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
                for (File pdfFile : pdfFiles) {
                    try (FileInputStream fis = new FileInputStream(pdfFile)) {
                        ZipEntry zipEntry = new ZipEntry(pdfFile.getName());
                        zos.putNextEntry(zipEntry);
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, len);
                        }
                        zos.closeEntry();
                    }
                }
            }
        } finally {
            // 清理临时文件
            for (File pdfFile : pdfFiles) {
                if (!pdfFile.delete()) {
                    log.warn("无法删除临时文件: {}", pdfFile.getAbsolutePath());
                }
            }
            if (!tempDir.delete()) {
                log.warn("无法删除临时目录: {}", tempDir.getAbsolutePath());
            }
        }
    }

    @NotNull
    private List<ScoreVO.ScoreList> getScoreLists(Map<Integer, List<StudentAdultStudyVo>> termNumCourses) {
        List<StudentAdultStudyVo> studentAdultStudyVos1 = termNumCourses.get(1);
        List<StudentAdultStudyVo> studentAdultStudyVos2 = termNumCourses.get(2);
        List<StudentAdultStudyVo> studentAdultStudyVos3 = termNumCourses.get(3);
        List<StudentAdultStudyVo> studentAdultStudyVos4 = termNumCourses.get(4);
        List<StudentAdultStudyVo> studentAdultStudyVos5 = termNumCourses.get(5);
        // 获取 size 最大的集合
        int maxSize = termNumCourses.values().stream()
                .mapToInt(List::size)
                .max()
                .orElse(0);
        //保持表格最后空两行：+2
        List<ScoreVO.ScoreList> scoreLists = new ArrayList<>();
        for (int i = 0; i < maxSize + 2; i++) {
            ScoreVO.ScoreList score = new ScoreVO.ScoreList();
            int indexNum = i + 1;
            score.setIndexNum(indexNum);
            if (CollectionUtils.isNotEmpty(studentAdultStudyVos1)) {
                int y = i + 1;
                if (y <= studentAdultStudyVos1.size()) {
                    StudentAdultStudyVo studentAdultStudyVo1 = studentAdultStudyVos1.get(i);
                    score.setScore1(studentAdultStudyVo1.getCompositeScore());
                    score.setCourseName1(studentAdultStudyVo1.getCourseName());
                }
            }
            if (CollectionUtils.isNotEmpty(studentAdultStudyVos2)) {
                int y = i + 1;
                if (y <= studentAdultStudyVos2.size()) {
                    StudentAdultStudyVo studentAdultStudyVo2 = studentAdultStudyVos2.get(i);
                    score.setScore2(studentAdultStudyVo2.getCompositeScore());
                    score.setCourseName2(studentAdultStudyVo2.getCourseName());

                }
            }
            if (CollectionUtils.isNotEmpty(studentAdultStudyVos3)) {
                int y = i + 1;
                if (y <= studentAdultStudyVos3.size()) {
                    StudentAdultStudyVo studentAdultStudyVo3 = studentAdultStudyVos3.get(i);
                    score.setScore3(studentAdultStudyVo3.getCompositeScore());
                    score.setCourseName3(studentAdultStudyVo3.getCourseName());
                }
            }
            if (CollectionUtils.isNotEmpty(studentAdultStudyVos4)) {
                int y = i + 1;
                if (y <= studentAdultStudyVos4.size()) {
                    StudentAdultStudyVo studentAdultStudyVo4 = studentAdultStudyVos4.get(i);
                    score.setScore4(studentAdultStudyVo4.getCompositeScore());
                    score.setCourseName4(studentAdultStudyVo4.getCourseName());
                }
            }
            if (CollectionUtils.isNotEmpty(studentAdultStudyVos5)) {
                int y = i + 1;
                if (y <= studentAdultStudyVos5.size()) {
                    StudentAdultStudyVo studentAdultStudyVo5 = studentAdultStudyVos5.get(i);
                    score.setScore5(studentAdultStudyVo5.getCompositeScore());
                    score.setCourseName5(studentAdultStudyVo5.getCourseName());
                }
            }
            scoreLists.add(score);
        }
        return scoreLists;
    }

    private void createtermNumInfo(ScoreVO.StudentInfo studentInfo, Map<Integer, List<StudentAdultStudyVo>> termNumCourses) {
        List<StudentAdultStudyVo> studyVos1 = termNumCourses.get(1);
        if (CollectionUtils.isNotEmpty(studyVos1)) {
            Date termStart = studyVos1.get(0).getTermStart();
            List<StudentAdultStudyVo> studyVos2 = termNumCourses.get(2);
            Date termEnd = studyVos2 != null ? studyVos2.get(0).getTermEnd() : studyVos1.get(0).getTermEnd();
            String schoolOneYear = DateUtil.format(termStart,"yyyy.MM") + "-" + DateUtil.format(termEnd,"yyyy.MM");

            List<StudentAdultStudyVo> studyVos3 = termNumCourses.get(3);
            if (CollectionUtils.isNotEmpty(studyVos3)) {
                Date termStart1 = studyVos3.get(0).getTermStart();
                List<StudentAdultStudyVo> studyVos4 = termNumCourses.get(4);
                Date termEnd1 = studyVos4 != null ? studyVos4.get(0).getTermEnd() : studyVos3.get(0).getTermEnd();
                String schoolTwoYear = DateUtil.format(termStart1,"yyyy.MM") + "-" + DateUtil.format(termEnd1,"yyyy.MM");;
                studentInfo.setSchoolTwoYear(schoolTwoYear);

                List<StudentAdultStudyVo> studyVos5 = termNumCourses.get(5);
                if (studyVos5 != null) {
                    Date termStart2 = studyVos5.get(0).getTermStart();
                    Date termEnd2 = studyVos5.get(0).getTermEnd();
                    String schoolThreeYear = DateUtil.format(termStart2,"yyyy.MM") + "-" + DateUtil.format(termEnd2,"yyyy.MM");
                    studentInfo.setSchoolThreeYear(schoolThreeYear);
                }
            }
            studentInfo.setSchoolOneYear(schoolOneYear);
        }
    }

    /**
     * 计算统计比例
     *
     * @param list
     * @param type
     * @return
     */
    private List<ExamScoreTableExport> calculateTheScore(List<ExamScoreTableExport> list, ScoreListTypeEnum type) {
        list.forEach(score -> {
            BigDecimal nineNumRatio = score.getNineNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getNineNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            BigDecimal eightNumRatio = score.getEightNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getEightNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            BigDecimal sevenNumRatio = score.getSevenNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getSevenNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            BigDecimal sixNumRatio = score.getSixNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getSixNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            BigDecimal lessSixNumRatio = score.getLessSixNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getLessSixNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));

            BigDecimal passNumRatio = score.getPassNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getPassNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            BigDecimal noPassNumRatio = score.getNoPassNum().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : score.getNoPassNum().divide(score.getTotalStudentNum(), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));

            score.setNineNumRatio(String.format("%s(%s%%)", score.getNineNum(), nineNumRatio.doubleValue()));
            score.setEightNumRatio(String.format("%s(%s%%)", score.getEightNum(), eightNumRatio.doubleValue()));
            score.setSevenNumRatio(String.format("%s(%s%%)", score.getSevenNum(), sevenNumRatio.doubleValue()));
            score.setSixNumRatio(String.format("%s(%s%%)", score.getSixNum(), sixNumRatio.doubleValue()));
            score.setLessSixNumRatio(String.format("%s(%s%%)", score.getLessSixNum(), lessSixNumRatio.doubleValue()));
            score.setPassNumRatio(String.format("%s(%s%%)", score.getPassNum(), passNumRatio.doubleValue()));
            score.setNoPassNumRatio(String.format("%s(%s%%)", score.getNoPassNum(), noPassNumRatio.doubleValue()));

            switch (type) {
                case ALONE_EXAM:
                    score.setExaeType("独立考试");
                    break;
                case UNIFIED_EXAM:
                    score.setExaeType("统考过程性");
                    break;
                case COMPUTER_EXAM:
                    score.setExaeType("机考");
                    break;
                default:
                    break;
            }
        });
        return list;
    }

    /**
     * 删除录入成绩逻辑处理
     *
     * @param list
     * @param type
     * @return
     */
    private List<ScoreListDetails> enterScoreDispose(List<ScoreListDetails> list, ScoreListTypeEnum type) {
        List<ScoreListDetails> newList = new ArrayList<>();
        switch (type) {
            case FINAL_EXAM:
                for (ScoreListDetails details : list) {
                    // 未录入成绩跳过
                    if (details.getExamScore() == null && details.getScoreType() == null) {
                        continue;
                    }
                    details.setExamScore(BigDecimal.ZERO);
                    details.setDataSources("线上成绩");
                    // 重新计算综合成绩(保留2位，舍弃第3位)
                    // 学习进度
                    BigDecimal studySocre = details.getStudyPlan().multiply(BigDecimalUtils.percentToDecimalsUp(details.getStudyPlanRatio(), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                    // 资料进度成绩
                    BigDecimal informationSocre = details.getInformationPlan().multiply(BigDecimalUtils.percentToDecimalsUp(details.getInformationRatio(), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                    // 作业成绩
                    BigDecimal jobScore = details.getJobScore().multiply(BigDecimalUtils.percentToDecimalsUp(details.getJobScoreRatio(), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                    // 练习成绩
                    BigDecimal exerciseScore = details.getExerciseScore().multiply(BigDecimalUtils.percentToDecimalsUp(details.getExerciseScoreRatio(), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                    // 综合成绩
                    BigDecimal compositeScore = studySocre.add(studySocre).add(informationSocre).add(jobScore).add(exerciseScore);

                    details.setCompositeScore(compositeScore);
                    // 是否合格
                    details.setIsPass(compositeScore.compareTo(details.getPassScore()) >= 0 ? 1 : 0);

                    newList.add(details);
                }
                break;
            case UNIFIED_EXAM:
                for (ScoreListDetails details : list) {
                    // 未录入成绩跳过
                    if (details.getUnifiedMaxScore() == null) {
                        continue;
                    }
                    details.setUnifiedMaxScore(BigDecimal.ZERO);
                    details.setDataSources("线上成绩");
                    details.setIsPass(0);
                    // 重新计算综合成绩(保留2位，舍弃第3位)
                    if (details.getUnifiedScore() == null || details.getUnifiedScore().compareTo(BigDecimal.ZERO) <= 0) {
                        details.setCompositeScore(BigDecimal.ZERO);
                    } else {
                        // 统考过程考试
                        BigDecimal unifiedScore = details.getUnifiedScore().multiply(BigDecimalUtils.percentToDecimalsUp(details.getUnifiedScoreRatio(), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                        details.setCompositeScore(unifiedScore);
                    }

                    newList.add(details);
                }

                break;

            case COMPUTER_EXAM:
                for (ScoreListDetails details : list) {
                    // 未录入成绩跳过
                    if (details.getWorkScore() == null || details.getComputerScore() == null) {
                        continue;
                    }
                    details.setWorkScore(BigDecimal.ZERO);
                    details.setComputerScore(null);
                    details.setDataSources("线上成绩");
                    // 重新计算综合成绩(保留2位，舍弃第3位)
                    // 线上学习成绩
                    BigDecimal internetPlan = BigDecimal.ZERO;
                    if ((details.getInternetPlan() != null && details.getInternetPlan().compareTo(BigDecimal.ZERO) <= 0) && (details.getInternetPlanRatio() != null && details.getInternetPlanRatio().compareTo(BigDecimal.ZERO) <= 0)) {
                        internetPlan = details.getInternetPlan().multiply(BigDecimalUtils.percentToDecimalsUp(details.getInternetPlanRatio(), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                    }
                    // 线上成绩
                    BigDecimal lineComputerScore = BigDecimal.ZERO;
                    if ((details.getLineComputerScore() != null && details.getLineComputerScore().compareTo(BigDecimal.ZERO) <= 0) && (details.getComputerScoreRatio() != null && details.getComputerScoreRatio().compareTo(BigDecimal.ZERO) <= 0)) {
                        lineComputerScore = details.getLineComputerScore().multiply(BigDecimalUtils.percentToDecimalsUp(details.getComputerScoreRatio(), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
                    }
                    BigDecimal compositeScore = internetPlan.add(lineComputerScore);
                    details.setCompositeScore(compositeScore);
                    details.setIsPass(compositeScore.compareTo(details.getPassScore()) >= 0 ? 1 : 0);

                    newList.add(details);
                }
                break;
            default:
                throw new BaseException("类型错误");
        }
        return newList;
    }

    /**
     * 在校成绩模板
     *
     * @param platformName
     * @param response
     */
    public void finalExam(String platformName, HttpServletResponse response) {
        String fileName = "在校成绩模板表";
        OutputStream outputStream = ExcelUtils.setMimeTypeAndFileName(fileName, response, MimeTypeEnum.XLS);
        List<FinalExamScoreImport> list = new ArrayList<>();
        FinalExamScoreImport scoreImport = new FinalExamScoreImport();
        scoreImport.setPlatName(platformName);
        list.add(scoreImport);
        List<String> scoreTypeList = List.of("正考", "补考", "缓考", "重修");
        try {
            EasyExcel.write(outputStream, FinalExamScoreImport.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new CustomSheetWriteHandler())
                    .registerWriteHandler(ExcelUtils.styleStrategy())
                    .registerWriteHandler(new SheetWriteHandler() {
                        @Override
                        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                            Sheet sheet = writeSheetHolder.getCachedSheet();
                            DataValidationHelper validationHelper = ((Sheet) sheet).getDataValidationHelper();

                            DataValidationConstraint validationConstraint = null;
                            CellRangeAddressList addressList = null;
                            DataValidation validation = null;
                            //课程名称
                            if (CollectionUtils.isNotEmpty(scoreTypeList)) {
                                validationConstraint = validationHelper.createExplicitListConstraint(scoreTypeList.toArray(new String[scoreTypeList.size()]));
                                addressList = new CellRangeAddressList(1, 5000, 12, 12);
                                validation = validationHelper.createValidation(validationConstraint, addressList);
                                validation.setShowErrorBox(true);
                                validation.setSuppressDropDownArrow(true);
                                sheet.addValidationData(validation);
                            }
                        }
                    }).registerWriteHandler(new CustomSheetWriteHandler()).sheet(fileName).doWrite(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ExcelException("获取模板失败");
        }
    }

    /**
     * 统考成绩模板
     *
     * @param platformName
     * @param response
     */
    public void unifiedExam(String platformName, HttpServletResponse response) {
        String fileName = "统考成绩模板表";
        OutputStream outputStream = ExcelUtils.setMimeTypeAndFileName(fileName, response, MimeTypeEnum.XLS);
        List<UnifiedExamScoreImport> list = new ArrayList<>();
        UnifiedExamScoreImport scoreImport = new UnifiedExamScoreImport();
        scoreImport.setPlatName(platformName);

        list.add(scoreImport);
        try {
            EasyExcel.write(outputStream, UnifiedExamScoreImport.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new CustomSheetWriteHandler())
                    .registerWriteHandler(ExcelUtils.styleStrategy())
                    .sheet(fileName).doWrite(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ExcelException("获取模板失败");
        }
    }

    /**
     * 省考成绩模板
     *
     * @param platformName
     * @param response
     */
    public void computerExam(String platformName, HttpServletResponse response) {
        String fileName = "省考成绩模板表";
        OutputStream outputStream = ExcelUtils.setMimeTypeAndFileName(fileName, response, MimeTypeEnum.XLS);
        List<ComputerExamScoreImport> list = new ArrayList<>();
        ComputerExamScoreImport scoreImport = new ComputerExamScoreImport();
        scoreImport.setPlatName(platformName);

        list.add(scoreImport);
        try {
            EasyExcel.write(outputStream, ComputerExamScoreImport.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new CustomSheetWriteHandler())
                    .registerWriteHandler(ExcelUtils.styleStrategy())
                    .sheet(fileName).doWrite(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ExcelException("获取模板失败");
        }
    }

}
