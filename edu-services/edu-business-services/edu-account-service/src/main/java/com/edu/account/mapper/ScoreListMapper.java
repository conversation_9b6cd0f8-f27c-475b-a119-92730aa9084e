package com.edu.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.account.domain.OfflineScoreList;
import com.edu.account.domain.ScoreList;
import com.edu.account.enums.ScoreListTypeEnum;
import com.edu.account.excel.ComputerExamScoreImport;
import com.edu.account.excel.FinalExamScoreImport;
import com.edu.account.excel.UnifiedExamScoreImport;
import com.edu.account.excel.export.*;
import com.edu.account.model.dto.StudentConditionDTO;
import com.edu.account.model.dto.school.GraduationRuleDTO;
import com.edu.account.model.query.ExaminationStatisticsQuery;
import com.edu.account.model.query.ScoreListQuery;
import com.edu.account.model.vo.GraduationRuleScoreVO;
import com.edu.account.model.vo.ScoreListVo;
import com.edu.account.valueobject.ScoreListRelationshipBo;
import com.edu.adult.domain.OrgResultRatio;
import com.edu.adult.domain.SpecialtyPlanCourse;
import com.edu.adult.model.query.student.pc.StudentMyExamQuery;
import com.edu.adult.model.vo.shcool.pc.StudentAdultStudyVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
*  <AUTHOR>
*  @DESCRIPTION 成绩管理Mapper
*  @email <EMAIL>
*  2023-10-31
*/

public interface ScoreListMapper extends BaseMapper<ScoreList> {

      /***
       * 成绩管理主信息查询
       * @param query
       * @return
       */
      List<Long> selectRelationshipQuery(@Param("query") ScoreListQuery query);

      List<ScoreListVo> selectQuery(@Param("ids") List<Long> ids,@Param("type") ScoreListTypeEnum type);

      /**
       * 统考主表查询
       *
       * @param item
       * @return
       */
      ScoreList getInfoByUnifiedExam(@Param("item") UnifiedExamScoreImport item);

      /**
       * 省考主表查询
       * @param item
       * @return
       */
      ScoreList getInfoByComputerExam(@Param("item") ComputerExamScoreImport item);

      List<ScoreListExport> selectDownExcelQuery(@Param("query")ScoreListQuery query);

      /**
       * 期末考试参考情况图统计
       *
       * @param query
       */
      List<ExamPeopleIconExport> selectFinalExamPeopleIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 独立考试参考情况图统计
       * @param query
       */
      List<ExamPeopleIconExport> selectAloneExamPeopleIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 统考考试参考情况图统计
       * @param query
       */
      List<ExamPeopleIconExport> selectUnifiedExamPeopleIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 省考考试参考情况图统计
       * @param query
       */
      List<ExamPeopleIconExport> selectComputerExamPeopleIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 期末考试参考情况表统计
       * @param query
       * @return
       */
      List<ExamPeopleTableExport> selectFinalExamPeopleTable(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 独立考试参考情况表统计
       * @param query
       */
      List<ExamPeopleTableExport> selectAloneExamPeopleTable(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 统考考试参考情况表统计
       * @param query
       * @return
       */
      List<ExamPeopleTableExport> selectUnifiedExamPeopleTable(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 省考考试参考情况表统计
       * @param query
       */
      List<ExamPeopleTableExport> selectComputerExamPeopleTable(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 获得期末考试集体学生条件
       *
       * @param plId
       * @param orgCode
       * @return
       */
      List<StudentConditionDTO> getFinalCollectivityStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode);

      /**
       * 获得期末考试个人学生条件
       * @param plId
       * @param orgCode
       * @return
       */
      List<StudentConditionDTO> getFinalPersonageStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode);

      /**
       * 获得独立考试集体学生条件
       * @param plId
       * @param orgCode
       * @param batchId
       * @return
       */
      List<StudentConditionDTO> getAloneCollectivityStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode, @Param("batchId") String batchId);

      /**
       * 获得独立考试个人学生条件
       *
       * @param plId
       * @param orgCode
       * @param batchId
       * @return
       */
      List<StudentConditionDTO> getAlonePersonageStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode, @Param("batchId") String batchId);

      /**
       * 获得统考考试集体学生条件
       * @param plId
       * @param orgCode
       * @param batchId
       * @return
       */
      List<StudentConditionDTO> getUnifiedCollectivityStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode, @Param("batchId") String batchId);

      /**
       * 获得统考考试个人学生条件
       *
       * @param plId
       * @param orgCode
       * @param batchId
       * @return
       */
      List<StudentConditionDTO> getUnifiedPersonageStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode, @Param("batchId") String batchId);

      /**
       * 获得机考考试集体学生条件
       *
       * @param plId
       * @param orgCode
       * @param batchId
       * @return
       */
      List<StudentConditionDTO> getComputerCollectivityStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode, @Param("batchId") String batchId);

      /**
       * 获得机考考试个人学生条件
       *
       * @param plId
       * @param orgCode
       * @param batchId
       * @return
       */
      List<StudentConditionDTO> getComputerPersonageStudent(@Param("plId") Long plId, @Param("orgCode") String orgCode, @Param("batchId") String batchId);

      /**
       * 期末考试成绩情况图统计
       * @param query
       * @return
       */
      List<ExamScoreIconExport> selectFinalExamScoreIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 独立考试成绩情况图统计
       * @param query
       * @return
       */
      List<ExamScoreIconExport> selectAloneExamScoreIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 统考过程考试成绩情况图统计
       * @param query
       * @return
       */
      List<ExamScoreIconExport> selectUnifiedExamScoreIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 省考机考考试成绩情况图统计
       * @param query
       * @return
       */
      List<ExamScoreIconExport> selectComputerExamScoreIcon(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 期末考试成绩情况表统计
       * @param query
       * @return
       */
      List<ExamScoreTableExport> selectFinalExamScoreTable(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 独立考试成绩情况表统计
       * @param query
       * @return
       */
      List<ExamScoreTableExport> selectAloneExamScoreTable(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 统考过程考试成绩情况表统计
       * @param query
       * @return
       */
      List<ExamScoreTableExport> selectUnifiedExamScoreTable(@Param("query") ExaminationStatisticsQuery query);

      /**
       * 省考机考考试成绩情况表统计
       * @param query
       * @return
       */
      List<ExamScoreTableExport> selectComputerExamScoreTable(@Param("query") ExaminationStatisticsQuery query);


    ScoreList selectStudentAdultScoreList(@Param("item") FinalExamScoreImport item);

      /**
       * 毕业成绩
       * @param dto
       * @return
       */
      List<GraduationRuleScoreVO> getGraduationRuleScore(@Param("dto") GraduationRuleDTO dto);

    Long getStudentTestNumber(StudentMyExamQuery examQuery);

      Long getTestBKNumber(StudentMyExamQuery query);

      List<ExamPeopleTableExport> selectFinalExamPeopleTableList(@Param("query")ExaminationStatisticsQuery query);

    Long getRealityNumber(StudentMyExamQuery examQuery);

    OrgResultRatio selectSchoolRatio(String orgCode);

    OfflineScoreList selectOfflineCourse(@Param("gradeId") Long gradeId,@Param("specialtyId") Long specialtyId,@Param("courseId") Long courseId);

    SpecialtyPlanCourse selectPlanCourseRatio(@Param("orgCode") String orgCode,@Param("gradeId") Long gradeId,
                                              @Param("specialtyId") Long specialtyId,@Param("courseId") Long courseId,
                                             @Param("gradeTerm")Integer gradeTerm, @Param("levelId")Long levelId);

    List<StudentAdultStudyVo> selectScoreVoList(ScoreListQuery query);
}
