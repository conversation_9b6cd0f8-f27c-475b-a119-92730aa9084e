package com.edu.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.account.domain.FinanceSettlementOrganization;
import com.edu.account.enums.FinanceSettlementOrgAuditStatusEnum;
import com.edu.account.model.dto.school.FinanceSettlementOrgBatchRefundRateDTO;
import com.edu.account.model.query.FinanceSettlementOrganizationQuery;
import com.edu.account.model.vo.FinanceSettlementOrganizationVo;
import com.edu.commons.model.commons.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算机构管理服务接口
 * @email <EMAIL>
 * @date 2025-07-25
 */
public interface FinanceSettlementOrganizationService extends IService<FinanceSettlementOrganization> {

    /**
     * 分页查询结算机构列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<FinanceSettlementOrganizationVo> getPageList(FinanceSettlementOrganizationQuery query);

    /**
     * 财务结算时，获取机构统计列表
     * @param query
     * @return
     */
    PageInfo<FinanceSettlementOrganizationVo> getPageListVo(FinanceSettlementOrganizationQuery query);

    /**
     * 根据结算批次ID获取机构列表
     *
     * @param settlementBatchId 结算批次ID
     * @return 机构列表
     */
    List<FinanceSettlementOrganizationVo> getBySettlementBatchId(Long settlementBatchId);

    /**
     * 创建结算机构
     *
     * @param dto 创建DTO
     */
    void createSettlementOrganization(FinanceSettlementOrganization dto);

    /**
     * 删除结算机构
     *
     * @param id 机构ID
     */
    void deleteSettlementOrganization(Long id);

    /**
     * 批量删除结算机构
     *
     * @param ids 机构ID列表
     */
    void batchDeleteSettlementOrganization(List<Long> ids);

    /**
     * 设置返费比例
     *
     * @param orgId      机构ID
     * @param refundRate 返费比例
     */
    void setRefundRate(Long orgId, BigDecimal refundRate);

    /**
     * 批量设置返费比例
     *
     * @param settlementBatchId 结算批次ID
     * @param dto               批量设置DTO
     */
    void batchSetRefundRate(Long settlementBatchId, FinanceSettlementOrgBatchRefundRateDTO dto);

    /**
     * 计算返费金额
     *
     * @param orgId 机构ID
     */
    void calculateRefundAmount(Long orgId);

    /**
     * 审核机构
     *
     * @param orgId       机构ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核意见
     */
    void auditOrganization(Long orgId, Integer auditStatus, String auditRemark);

    /**
     * 批量审核机构
     *
     * @param orgIds      机构ID列表
     * @param auditStatus 审核状态
     * @param auditRemark 审核意见
     */
    void batchAuditOrganization(List<Long> orgIds, FinanceSettlementOrgAuditStatusEnum auditStatus, String auditRemark);

    /**
     * 人工导入机构（Excel导入）
     *
     * @param settlementBatchId 结算批次ID
     * @param file              Excel文件
     * @return 导入结果
     */
    ImportResult importOrganizations(Long settlementBatchId, MultipartFile file);

    /**
     * 下载机构导入模板
     *
     * @return 模板文件字节数组
     */
    byte[] downloadImportTemplate();

    /**
     * 导出机构数据
     *
     * @param settlementBatchId 结算批次ID
     * @return 导出文件字节数组
     */
    byte[] exportOrganizations(Long settlementBatchId);


    /**
     * 导入结果
     */
    class ImportResult {
        private Integer totalCount;
        private Integer successCount;
        private Integer failCount;
        private List<String> errorMessages;

        // getters and setters
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        public Integer getSuccessCount() { return successCount; }
        public void setSuccessCount(Integer successCount) { this.successCount = successCount; }
        public Integer getFailCount() { return failCount; }
        public void setFailCount(Integer failCount) { this.failCount = failCount; }
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }
}
