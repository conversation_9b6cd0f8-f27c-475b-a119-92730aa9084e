package com.edu.account.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.account.domain.FinanceSettlementBatch;
import com.edu.account.domain.FinanceSettlementBusiness;
import com.edu.account.domain.FinanceSettlementStudent;
import com.edu.account.enums.FinanceSettlementBusinessTypeEnum;
import com.edu.account.mapper.FinanceSettlementBatchMapper;
import com.edu.account.mapper.FinanceSettlementBusinessMapper;
import com.edu.account.mapper.FinanceSettlementStudentMapper;
import com.edu.account.model.dto.FinanceSettlementStudentSyncData;
import com.edu.account.model.query.FinanceSettlementStudentQuery;
import com.edu.account.model.vo.FinanceSettlementBusinessVo;
import com.edu.account.model.vo.FinanceSettlementStudentVo;
import com.edu.account.rabbitmq.producer.FinanceSettlementStudentSyncProducer;
import com.edu.account.service.FinanceSettlementStudentService;
import com.edu.account.utils.FinanceSettlementExcelUtil;
import com.edu.commons.exception.BaseException;
import com.edu.commons.model.commons.PageInfo;
import com.edu.commons.rabbitmq.message.FinanceSettlementStudentMessage;
import com.edu.commons.utils.ConditionAssert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算学员明细管理Service实现
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceSettlementStudentServiceImpl extends ServiceImpl<FinanceSettlementStudentMapper, FinanceSettlementStudent>
        implements FinanceSettlementStudentService {

    private final FinanceSettlementBatchMapper financeSettlementBatchMapper;

    private final FinanceSettlementBusinessMapper financeSettlementBusinessMapper;

    private final FinanceSettlementStudentSyncProducer financeSettlementStudentSyncProducer;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public PageInfo<FinanceSettlementStudentVo> getPageList(FinanceSettlementStudentQuery query) {
        return PageInfo.of(query, () -> baseMapper.selectPageList(query));
    }


    @Override
    public List<FinanceSettlementStudentVo> getBySettlementBatchId(Long settlementBatchId) {
        if(settlementBatchId == null){
            throw new BaseException("结算批次ID不能为空");
        }
        FinanceSettlementStudentQuery query = new FinanceSettlementStudentQuery();
        query.setSettlementBatchId(settlementBatchId);
        List<FinanceSettlementStudentVo> financeSettlementStudentVos = baseMapper.selectPageList(query);
        return financeSettlementStudentVos;
    }

    @Override
    public List<FinanceSettlementStudentVo> getBySettlementOrgId(String twoOrgCode) {
        return List.of();
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void syncStudentDataApply(Long settlementBatchId) {
        log.info("同步学员缴费数据，批次ID：{}", settlementBatchId);

        FinanceSettlementBatch batch = financeSettlementBatchMapper.selectById(settlementBatchId);
        ConditionAssert.isTrue(batch == null, "财务结算批次不存在");
        //结算批次数据
        Long plId = batch.getPlId();
        if (plId == null) {
            throw new BaseException("结算批次中,平台ID未配置");
        }
        //结算业务数据
        List<FinanceSettlementBusinessVo> financeSettlementBusinessVos = financeSettlementBusinessMapper.selectBySettlementBatchId(settlementBatchId);

        try {
            //循环financeSettlementBusinessVos 将批次ID\批次业务ID给到MQ处理
            financeSettlementBusinessVos.forEach(financeSettlementBusinessVo -> {
                FinanceSettlementStudentMessage message = FinanceSettlementStudentMessage.builder()
                        .settlementBatchId(settlementBatchId)
                        .settlementBusinessId(financeSettlementBusinessVo.getId())
                        .build();
                //添加redis锁,防止60秒内重复点击同一个结算批次
                RLock lock = redissonClient.getLock("syncStudentDataApply:lock:settlementBatchId:" + settlementBatchId + "settlementBusinessId:"+financeSettlementBusinessVo.getId());
                try {
                    // 尝试获取锁，最多等待30秒，锁有效期60秒
                    boolean isLocked = lock.tryLock(30, 60, TimeUnit.SECONDS);
                    if (!isLocked) {
                        log.warn("定时任务-每日检测任务: 未获取到分布式锁，跳过本次任务执行");
                        return;
                    }
                    //发送MQ消息
                    financeSettlementStudentSyncProducer.syncSend(message);
                } catch (Exception e) {
                    log.error("获取分布式锁异常", e);
                } finally {
                    // 确保锁释放（当前线程持有锁时才释放）
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.info("释放分布式锁");
                    }
                }
                log.info("发送MQ消息成功，批次ID：{}", settlementBatchId);
            });

            log.info("学员缴费数据同步成功，批次ID：{}", settlementBatchId);
        } catch (Exception e) {
            log.error("同步学员缴费数据失败，批次ID：{}，错误：{}", settlementBatchId, e.getMessage(), e);
            throw new BaseException("同步学员缴费数据失败：" + e.getMessage());
        }
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void syncStudentDataProcess(Long settlementBatchId, Long settlementBusinessId) {
        // 获取结算批次数据
        FinanceSettlementBatch financeSettlementBatch = financeSettlementBatchMapper.selectById(settlementBatchId);
        FinanceSettlementBusinessTypeEnum businessType = financeSettlementBatch.getBusinessType();
        Long plId = financeSettlementBatch.getPlId();
        // 获取结算业务数据
        FinanceSettlementBusiness financeSettlementBusiness = financeSettlementBusinessMapper.selectById(settlementBusinessId);

        if (businessType.isPlatformFeeSettlement()) {
            // 平台费结算
            if (plId == 2L) {
                // 成教财务结算,根据结算批次业务同步录取大表学生数据
                syncAdultStudentDataBatchUpsert(settlementBatchId, settlementBusinessId, financeSettlementBatch, financeSettlementBusiness);
            } else if (plId == 3L) {
                // 自考财务结算,根据结算批次业务同步录取大表学生数据
                syncSelfStudentDataBatchUpsert(settlementBatchId, settlementBusinessId, financeSettlementBatch, financeSettlementBusiness);
            } else {
                throw new BaseException("平台ID配置错误,未知平台");
            }
        } else if (businessType.isAdvanceEnrollmentFeeSettlement()) {
            // 预报名费用结算
            if (plId == 2L) {
                // 成教预报名结算,根据预报名批次业务同步学生数据
                syncAdvanceEnrollmentData(settlementBatchId, settlementBusinessId, financeSettlementBatch, financeSettlementBusiness);
            } else if (plId == 3L) {
                // 自考预报名结算,根据预报名批次业务同步学生数据
                syncAdvanceEnrollmentData(settlementBatchId, settlementBusinessId, financeSettlementBatch, financeSettlementBusiness);
            } else {
                throw new BaseException("平台ID配置错误,未知平台");
            }
        }

        //按生成的学员表数据,其中的two_org_code,分组后的组织数据,更新结算组织,包含学生应缴与实缴金额汇总
        //插入组织逻辑
        baseMapper.insertSettlementOrgByBatch(settlementBatchId);
        //更新组织逻辑
        baseMapper.updateSettlementOrgByBatch(settlementBatchId);
    }

//    @Transactional(rollbackFor = Exception.class)
    public void syncAdultStudentDataBatchUpsert(Long settlementBatchId, Long settlementBusinessId,
                                                FinanceSettlementBatch batch, FinanceSettlementBusiness business) {
        log.info("开始批量智能同步成教学员数据，批次ID：{}，业务ID：{}", settlementBatchId, settlementBusinessId);

        int pageSize = 1000;
        int pageNum = 1;
        int totalProcessed = 0;

        try {
            while (true) {
                int offset = (pageNum - 1) * pageSize;

                // 分页查询成教学员数据
                List<FinanceSettlementStudentSyncData> adultStudents = baseMapper.selectAdultStudentsForSync(
                        batch.getPaymentStartTime(),
                        batch.getPaymentEndTime(),
                        business.getOrgId(), // 使用业务表中的机构ID
                        business.getBusinessBatchId(),
                        pageNum,
                        pageSize,
                        offset
                );

                if (CollectionUtils.isEmpty(adultStudents)) {
                    break;
                }

                // 处理每个学员数据，检查是否存在并决定插入或更新
                List<FinanceSettlementStudent> toInsert = new ArrayList<>();
                List<FinanceSettlementStudent> toUpdate = new ArrayList<>();
                int insertCount = 0;
                int updateCount = 0;

                for (FinanceSettlementStudentSyncData adult : adultStudents) {
                    // 检查是否已存在（基于enroll_id和批次ID）
                    FinanceSettlementStudent existing  = baseMapper.selectByEnrollIdAndBatch(
                            adult.getId(), settlementBatchId,settlementBusinessId);

                    FinanceSettlementStudent settlementStudent = createNewSettlementStudent(
                            adult, settlementBatchId, settlementBusinessId, batch, business);

                    if (existing != null) {
                        // 存在则更新
                        settlementStudent.setId(adult.getId());
                        settlementStudent.setPaymentYear(adult.getPaymentYear());
                        settlementStudent.setPaymentAcademicYear(adult.getPaymentAcademicYear());
                        settlementStudent.setShouldAmount(adult.getShouldAmount());
                        settlementStudent.setPaidAmount(adult.getPaidAmount());
                        //TODO 退款逻辑
                        settlementStudent.setRefundAmount(BigDecimal.ZERO);
                        settlementStudent.setPaymentOrderId(adult.getPaymentOrderId());
                        settlementStudent.setPaymentTime(adult.getPaymentTime());
                        settlementStudent.setPaymentStatus(adult.getPaymentStatus());
                        settlementStudent.setUpdateTime(new Date()); // 更新修改时间
                        toUpdate.add(settlementStudent);
                        updateCount++;
                    } else {
                        // 不存在则插入
                        toInsert.add(settlementStudent);
                        insertCount++;
                    }
                }

                // 批量插入新数据
                if (CollectionUtils.isNotEmpty(toInsert)) {
                    baseMapper.batchInsertSettlementStudents(toInsert);
                }

                // 批量更新已存在数据
                if (CollectionUtils.isNotEmpty(toUpdate)) {
                    baseMapper.batchUpdateSettlementStudents(toUpdate);
                }

                totalProcessed += adultStudents.size();

                if (totalProcessed % 1000 == 0) {
                    log.info("已处理成教学员数据：{} 条，本批次新增：{} 条，更新：{} 条",
                            totalProcessed, insertCount, updateCount);
                }

                pageNum++;
            }

            log.info("批量智能同步成教学员数据完成，总计处理：{} 条", totalProcessed);

        } catch (Exception e) {
            log.error("批量智能同步成教学员数据失败，批次ID：{}，错误：{}", settlementBatchId, e.getMessage(), e);
            throw new BaseException("批量智能同步成教学员数据失败：" + e.getMessage());
        }
    }

//    @Transactional(rollbackFor = Exception.class)
    public void syncSelfStudentDataBatchUpsert(Long settlementBatchId, Long settlementBusinessId,
                                               FinanceSettlementBatch batch, FinanceSettlementBusiness business) {
        log.info("开始批量智能同步自考学员数据，批次ID：{}，业务ID：{}", settlementBatchId, settlementBusinessId);

        int pageSize = 1000;
        int pageNum = 1;
        int totalProcessed = 0;

        try {
            while (true) {
                int offset = (pageNum - 1) * pageSize;

                // 分页查询自考学员数据
                List<FinanceSettlementStudentSyncData> selfStudents = baseMapper.selectSelfStudentsForSync(
                        batch.getPaymentStartTime(),
                        batch.getPaymentEndTime(),
                        business.getOrgId(),
                        business.getBusinessBatchId(),
                        pageNum,
                        pageSize,
                        offset
                );

                if (CollectionUtils.isEmpty(selfStudents)) {
                    break;
                }

                // 处理每个学员数据，检查是否存在并决定插入或更新
                List<FinanceSettlementStudent> toInsert = new ArrayList<>();
                List<FinanceSettlementStudent> toUpdate = new ArrayList<>();
                int insertCount = 0;
                int updateCount = 0;

                for (FinanceSettlementStudentSyncData self : selfStudents) {
                    // 检查是否已存在（基于enroll_id和批次ID）
                    FinanceSettlementStudent existing = baseMapper.selectByEnrollIdAndBatch(
                            self.getId(), settlementBatchId,settlementBusinessId);

                    FinanceSettlementStudent settlementStudent = createNewSettlementStudentFromSelf(
                            self, settlementBatchId, settlementBusinessId, batch, business);

                    if (existing != null) {
                        // 存在则更新
                        settlementStudent.setId(self.getId());
                        settlementStudent.setPaymentYear(self.getPaymentYear());
                        settlementStudent.setPaymentAcademicYear(self.getPaymentAcademicYear());
                        settlementStudent.setShouldAmount(self.getShouldAmount());
                        settlementStudent.setPaidAmount(self.getPaidAmount());
                        //TODO 退款逻辑
                        settlementStudent.setRefundAmount(BigDecimal.ZERO);
                        settlementStudent.setPaymentOrderId(self.getPaymentOrderId());
                        settlementStudent.setPaymentTime(self.getPaymentTime());
                        settlementStudent.setPaymentStatus(self.getPaymentStatus());
                        settlementStudent.setUpdateTime(new Date()); // 更新修改时间
                        toUpdate.add(settlementStudent);
                        updateCount++;
                    } else {
                        // 不存在则插入
                        toInsert.add(settlementStudent);
                        insertCount++;
                    }
                }

                // 批量插入新数据
                if (CollectionUtils.isNotEmpty(toInsert)) {
                    baseMapper.batchInsertSettlementStudents(toInsert);
                }

                // 批量更新已存在数据
                if (CollectionUtils.isNotEmpty(toUpdate)) {
                    baseMapper.batchUpdateSettlementStudents(toUpdate);
                }

                totalProcessed += selfStudents.size();
                pageNum++;

                if (totalProcessed % 1000 == 0) {
                    log.info("已处理自考学员数据：{} 条，本批次新增：{} 条，更新：{} 条",
                            totalProcessed, insertCount, updateCount);
                }
            }

            log.info("批量智能同步自考学员数据完成，总计处理：{} 条", totalProcessed);

        } catch (Exception e) {
            log.error("批量智能同步自考学员数据失败，批次ID：{}，错误：{}", settlementBatchId, e.getMessage(), e);
            throw new BaseException("批量智能同步自考学员数据失败：" + e.getMessage());
        }
    }

    private FinanceSettlementStudent createNewSettlementStudent(FinanceSettlementStudentSyncData adult,
                                                                Long settlementBatchId,
                                                                Long settlementBusinessId,
                                                                FinanceSettlementBatch batch,
                                                                FinanceSettlementBusiness business) {
        return FinanceSettlementStudent.builder()
                .settlementBatchId(settlementBatchId)
                .settlementBusinessId(settlementBusinessId)
                .enrollId(adult.getId()) // 存储自考学员表的ID
                .oneOrgId(adult.getOneOrgId())
                .oneOrgCode(adult.getOneOrgCode())
                .twoOrgId(adult.getTwoOrgId())
                .twoOrgCode(adult.getTwoOrgCode())
                .studentId(adult.getStudentId())
                .studentName(adult.getStudentName())
                .idNum(adult.getIdNum())
                .shouldAmount(adult.getShouldAmount())
                .paidAmount(adult.getPaidAmount())
                .refundAmount(calculateRefundAmount(adult.getPaidAmount(), BigDecimal.ONE))
                .paymentOrderId(adult.getPaymentOrderId())
                .paymentTime(adult.getPaymentTime())
                .paymentStatus(adult.getPaymentStatus())
                .paymentYear(adult.getPaymentYear())
                .paymentAcademicYear(adult.getPaymentAcademicYear())
                .createTime(new Date())
                .build();
    }

    private FinanceSettlementStudent createNewSettlementStudentFromSelf(FinanceSettlementStudentSyncData self,
                                                                        Long settlementBatchId,
                                                                        Long settlementBusinessId,
                                                                        FinanceSettlementBatch batch,
                                                                        FinanceSettlementBusiness business) {
        return FinanceSettlementStudent.builder()
                .settlementBatchId(settlementBatchId)
                .settlementBusinessId(settlementBusinessId)
                .enrollId(self.getId()) // 存储自考学员表的ID
                .oneOrgId(self.getOneOrgId())
                .oneOrgCode(self.getOneOrgCode())
                .twoOrgId(self.getTwoOrgId())
                .twoOrgCode(self.getTwoOrgCode())
                .studentId(self.getStudentId())
                .studentName(self.getStudentName())
                .idNum(self.getIdNum())
                .shouldAmount(self.getShouldAmount())
                .paidAmount(self.getPaidAmount())
                .refundAmount(calculateRefundAmount(self.getPaidAmount(), BigDecimal.ONE))
                .paymentOrderId(self.getPaymentOrderId())
                .paymentTime(self.getPaymentTime())
                .paymentStatus(self.getPaymentStatus())
                .paymentYear(self.getPaymentYear())
                .paymentAcademicYear(self.getPaymentAcademicYear())
                .createTime(new Date())
                .build();
    }

    private BigDecimal calculateRefundAmount(BigDecimal paidAmount, BigDecimal refundRate) {
        if (paidAmount == null || refundRate == null) {
            return BigDecimal.ZERO;
        }
        return paidAmount.multiply(refundRate).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

//    @Transactional(rollbackFor = Exception.class)
    public void syncAdvanceEnrollmentData(Long settlementBatchId, Long settlementBusinessId,
                                               FinanceSettlementBatch batch, FinanceSettlementBusiness business) {
        log.info("开始同步预报名学员数据，批次ID：{}，业务ID：{}", settlementBatchId, settlementBusinessId);

        // 预报名费用结算逻辑
        // 根据结算批次业务ID中的预报名批次ID查询指定预报名批次，获取预报名批次所有学生数据
        List<FinanceSettlementStudentSyncData> advanceEnrollmentStudents = baseMapper.selectAdvanceEnrollmentStudentsForSync(business.getBusinessBatchId());
        if (CollectionUtils.isEmpty(advanceEnrollmentStudents)) {
            log.info("未查询到预报名学员数据，批次ID：{}", settlementBatchId);
            return;
        }

        for (FinanceSettlementStudentSyncData self : advanceEnrollmentStudents) {
            // 检查是否已存在（基于enroll_id和批次ID）
            FinanceSettlementStudent existing = baseMapper.selectByEnrollIdAndBatch(
                    self.getId(), settlementBatchId,settlementBusinessId);

            FinanceSettlementStudent settlementStudent = createNewSettlementStudentFromSelf(
                    self, settlementBatchId, settlementBusinessId, batch, business);

            // 处理每个学员数据，检查是否存在并决定插入或更新
            List<FinanceSettlementStudent> toInsert = new ArrayList<>();
            List<FinanceSettlementStudent> toUpdate = new ArrayList<>();

            if (existing != null) {
                // 存在则更新
                settlementStudent.setId(self.getId());
                settlementStudent.setPaymentYear(self.getPaymentYear());
                settlementStudent.setPaymentAcademicYear(self.getPaymentAcademicYear());
                settlementStudent.setShouldAmount(self.getShouldAmount());
                settlementStudent.setPaidAmount(self.getPaidAmount());
                //TODO 退款逻辑
                settlementStudent.setRefundAmount(BigDecimal.ZERO);
                settlementStudent.setPaymentOrderId(self.getPaymentOrderId());
                settlementStudent.setPaymentTime(self.getPaymentTime());
                settlementStudent.setPaymentStatus(self.getPaymentStatus());
                settlementStudent.setUpdateTime(new Date()); // 更新修改时间
                toUpdate.add(settlementStudent);
            } else {
                // 不存在则插入
                toInsert.add(settlementStudent);
            }

            // 批量插入新数据
            if (CollectionUtils.isNotEmpty(toInsert)) {
                baseMapper.batchInsertSettlementStudents(toInsert);
            }

            // 批量更新已存在数据
            if (CollectionUtils.isNotEmpty(toUpdate)) {
                baseMapper.batchUpdateSettlementStudents(toUpdate);
            }

            //按生成的学员表数据,其中的two_org_code,分组后的组织数据,更新结算组织,包含学生应缴与实缴金额汇总
            //插入逻辑
            baseMapper.insertSettlementOrgByBatch(settlementBatchId);
            //更新逻辑
            baseMapper.updateSettlementOrgByBatch(settlementBatchId);
        }

        log.info("成教预报名学员数据同步完成，批次ID：{}", settlementBatchId);
    }

    @Override
    public void deleteStudentDetail(Long id) {
        log.info("删除学员明细，ID：{}", id);

        FinanceSettlementStudent detail = getById(id);
        ConditionAssert.isTrue(detail == null, "学员明细不存在");

        removeById(id);

        log.info("学员明细删除成功，ID：{}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteStudentDetail(List<Long> ids) {
        log.info("批量删除学员明细，数量：{}", ids.size());

        if (CollectionUtils.isNotEmpty(ids)) {
            baseMapper.batchDeleteByIds(ids);
        }

        log.info("批量删除学员明细成功，数量：{}", ids.size());
    }

    @Override
    public byte[] exportStudentDetails(Long settlementBatchId) {
        log.info("导出学员明细数据，批次ID：{}", settlementBatchId);

        try {
            List<FinanceSettlementStudentVo> studentDetails = getBySettlementBatchId(settlementBatchId);
            return FinanceSettlementExcelUtil.exportStudentDetails(studentDetails);
        } catch (Exception e) {
            log.error("导出学员明细数据失败，批次ID：{}，错误：{}", settlementBatchId, e.getMessage(), e);
            throw new BaseException("导出学员明细数据失败：" + e.getMessage());
        }
    }

    @Override
    public byte[] exportStudentDetailsByOrgCode(String twoOrgCode) {
        log.info("按机构导出学员明细数据，机构ID：{}", twoOrgCode);

        try {
            List<FinanceSettlementStudentVo> studentDetails = getBySettlementOrgId(twoOrgCode);
            return FinanceSettlementExcelUtil.exportStudentDetails(studentDetails);
        } catch (Exception e) {
            log.error("按机构导出学员明细数据失败，机构CodeD：{}，错误：{}", twoOrgCode, e.getMessage(), e);
            throw new BaseException("导出学员明细数据失败：" + e.getMessage());
        }
    }

    @Override
    public StudentDetailStatistics getStatistics(Long settlementBatchId) {
        log.info("获取学员明细统计信息，批次ID：{}", settlementBatchId);

        return baseMapper.getStatistics(settlementBatchId);
    }

    @Override
    public StudentDetailStatistics getStatisticsByOrg(Long settlementOrgId) {
        log.info("获取机构学员明细统计信息，机构ID：{}", settlementOrgId);

        return baseMapper.getStatisticsByOrg(settlementOrgId);
    }

    @Override
    public ValidationResult validateStudentDetails(Long settlementBatchId) {
        log.info("验证学员明细数据，批次ID：{}", settlementBatchId);

        ValidationResult result = new ValidationResult();
        result.setIsValid(true);
        result.setErrorMessages(new ArrayList<>());
        result.setWarningMessages(new ArrayList<>());

        try {
            List<String> errors = baseMapper.validateStudentDetails(settlementBatchId);

            if (CollectionUtils.isNotEmpty(errors)) {
                result.setIsValid(false);
                result.setErrorMessages(errors);
            }

            log.info("学员明细数据验证完成，批次ID：{}，是否有效：{}", settlementBatchId, result.getIsValid());
        } catch (Exception e) {
            log.error("验证学员明细数据失败，批次ID：{}，错误：{}", settlementBatchId, e.getMessage(), e);
            result.setIsValid(false);
            result.getErrorMessages().add("验证失败：" + e.getMessage());
        }

        return result;
    }
}
