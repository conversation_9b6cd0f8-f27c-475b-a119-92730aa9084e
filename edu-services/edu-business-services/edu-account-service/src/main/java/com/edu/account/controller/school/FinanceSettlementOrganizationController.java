package com.edu.account.controller.school;

import com.edu.account.domain.FinanceSettlementOrganization;
import com.edu.account.enums.FinanceSettlementOrgAuditStatusEnum;
import com.edu.account.model.dto.school.FinanceSettlementOrgBatchRefundRateDTO;
import com.edu.account.model.query.FinanceSettlementOrganizationQuery;
import com.edu.account.model.vo.FinanceSettlementOrganizationVo;
import com.edu.account.service.FinanceSettlementOrganizationService;
import com.edu.commons.annotations.LogOperation;
import com.edu.commons.constants.CommonsConstants;
import com.edu.commons.controller.BaseController;
import com.edu.commons.model.Ret;
import com.edu.commons.model.commons.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.models.auth.In;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DESCRIPTION 财务结算批次管理Controller
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Api(tags = "后台School-财务结算批次组织管理")
@RestController
@Slf4j
@Validated
@RequiredArgsConstructor
@RequestMapping(CommonsConstants.ApiPrefix.SCHOOL + "/financeSettlementOrganization")
public class FinanceSettlementOrganizationController extends BaseController {

    private final FinanceSettlementOrganizationService financeSettlementOrganizationService;

    // ==================== 结算机构管理 ====================

    @ApiOperation(value = "结算机构列表", notes = "权限标识 financeSettlementOrganization:pageList")
    @PostMapping("/pageList")
    public Ret<PageInfo<FinanceSettlementOrganizationVo>> getOrganizationList(@RequestBody @Valid FinanceSettlementOrganizationQuery query) {
        if(query.getSettlementBatchId()==null){
            return Ret.fail("结算批次ID不能为空");
        }
        return Ret.success(financeSettlementOrganizationService.getPageList(query));
    }

    @ApiOperation(value = "财务结算机构列表", notes = "权限标识 financeSettlementOrganization:pageListVo")
    @PostMapping("/pageListVo")
    public Ret<PageInfo<FinanceSettlementOrganizationVo>> getOrganizationListVo(@RequestBody @Valid FinanceSettlementOrganizationQuery query) {
        if(query.getSettlementBatchId()==null){
            return Ret.fail("结算批次ID不能为空");
        }
        return Ret.success(financeSettlementOrganizationService.getPageListVo(query));
    }

    @ApiOperation(value = "设置返费比例", notes = "权限标识 financeSettlementOrganization:orgSetRate")
    @PutMapping("/orgSetRate/{id}")
    @LogOperation(value = "设置返费比例", type = LogOperation.Type.Update, entr = LogOperation.Entr.SCHOOL)
    public Ret<String> setRefundRate(
            @PathVariable @NotNull @ApiParam(value = "结算机构ID", required = true) Long id,
            @RequestParam @NotNull @ApiParam(value = "返费比例", required = true) BigDecimal refundRate) {
        financeSettlementOrganizationService.setRefundRate(id, refundRate);
        return Ret.success("设置成功");
    }

    @ApiOperation(value = "批量设置返费比例", notes = "权限标识 financeSettlementOrganization:orgBatchSetRate")
    @PutMapping("/orgBatchSetRate/{batchId}")
    @LogOperation(value = "批量设置返费比例", type = LogOperation.Type.Update, entr = LogOperation.Entr.SCHOOL)
    public Ret<String> batchSetRefundRate(
            @PathVariable @NotNull @ApiParam(value = "批次ID", required = true) Long batchId,
            @RequestBody @Valid FinanceSettlementOrgBatchRefundRateDTO dto) {
        financeSettlementOrganizationService.batchSetRefundRate(batchId, dto);
        return Ret.success("批量设置成功");
    }

    @ApiOperation(value = "创建财务结算业务", notes = "权限标识 financeSettlementOrganization:addOrUpdate")
    @PostMapping("/add")
    @LogOperation(value = "创建财务结算批次", type = LogOperation.Type.Insert, entr = LogOperation.Entr.SCHOOL)
    public Ret<String> createBatch(@RequestBody @Valid FinanceSettlementOrganization dto) {
        if (dto.getSettlementBatchId() == null) {
            return Ret.fail("批次ID不能为空");
        }
        financeSettlementOrganizationService.createSettlementOrganization(dto);
        return Ret.success("创建成功");
    }

    @ApiOperation(value = "财务结算业务详情", notes = "权限标识 financeSettlementOrganization:getById")
    @GetMapping("/getById/{id}")
    public Ret<FinanceSettlementOrganization> getBatchDetail(
            @PathVariable @NotNull @ApiParam(value = "业务ID", required = true) Long id) {
        return Ret.success(financeSettlementOrganizationService.getById(id));
    }

    @ApiOperation(value = "更新财务结算业务", notes = "权限标识 financeSettlementOrganization:addOrUpdate")
    @PutMapping("/update")
    public Ret<String> updateBatch(@RequestBody @Valid FinanceSettlementOrganization dto) {
        if (dto.getId() == null) {
            return Ret.fail("业务ID不能为空");
        }
        financeSettlementOrganizationService.updateById(dto);
        return Ret.success("更新成功");
    }

    @ApiOperation(value = "删除财务结算业务", notes = "权限标识 financeSettlementOrganization:delete")
    @DeleteMapping("/delete/{id}")
    @LogOperation(value = "删除财务结算业务", type = LogOperation.Type.Delete, entr = LogOperation.Entr.SCHOOL)
    public Ret<String> deleteBatch(
            @PathVariable @NotNull @ApiParam(value = "业务ID", required = true) Long id) {
        financeSettlementOrganizationService.deleteSettlementOrganization(id);
        return Ret.success("删除成功");
    }

    @ApiOperation(value = "审核机构", notes = "权限标识 financeSettlementOrganization:orgAudit")
    @PutMapping("/orgAudit/{id}")
    @LogOperation(value = "审核机构", type = LogOperation.Type.Update, entr = LogOperation.Entr.SCHOOL)
    public Ret<String> auditOrganization(
            @PathVariable @NotNull @ApiParam(value = "机构ID", required = true) Long id,
            @RequestParam @NotNull @ApiParam(value = "审核状态", required = true) Integer auditStatus,
            @RequestParam(required = false) @ApiParam(value = "审核意见") String auditRemark) {
        financeSettlementOrganizationService.auditOrganization(id, auditStatus, auditRemark);
        return Ret.success("审核成功");
    }

    @ApiOperation(value = "导入机构", notes = "权限标识 financeSettlementOrganization:import")
    @PostMapping("/import/{batchId}")
    @LogOperation(value = "导入机构", type = LogOperation.Type.Insert, entr = LogOperation.Entr.SCHOOL)
    public Ret<FinanceSettlementOrganizationService.ImportResult> importOrganizations(
            @PathVariable @NotNull @ApiParam(value = "批次ID", required = true) Long batchId,
            @RequestParam("file") MultipartFile file) {
        FinanceSettlementOrganizationService.ImportResult result = financeSettlementOrganizationService.importOrganizations(batchId, file);
        return Ret.success(result);
    }

    @ApiOperation(value = "下载机构导入模板", notes = "权限标识 financeSettlementOrganization:exportTemplate")
    @PostMapping("/export/template")
    public void downloadImportTemplate() {
        // 实现文件下载逻辑
    }

    @ApiOperation(value = "导出机构数据", notes = "权限标识 financeSettlementOrganization:export")
    @GetMapping("/export/{batchId}")
    public void exportOrganizations(
            @PathVariable @NotNull @ApiParam(value = "批次ID", required = true) Long batchId, HttpServletResponse response) {
        // 实现文件导出逻辑
        byte[] exportOrganizations = financeSettlementOrganizationService.exportOrganizations(batchId);
        // 实现文件下载逻辑
        response.setHeader("Content-Disposition", "attachment; filename=\"student_details.xlsx\"");
        response.addHeader("Content-Length", "" + exportOrganizations.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        try {
            response.getOutputStream().write(exportOrganizations);
        }catch (Exception e){
            throw new RuntimeException("导出机构数据失败");
        }
    }
}
