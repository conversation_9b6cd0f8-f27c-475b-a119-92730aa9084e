package com.edu.account.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.account.domain.FinanceSettlementBatch;
import com.edu.account.domain.FinanceSettlementOrganization;
import com.edu.account.enums.FinanceSettlementOrgAuditStatusEnum;
import com.edu.account.mapper.FinanceSettlementBatchMapper;
import com.edu.account.mapper.FinanceSettlementOrganizationMapper;
import com.edu.account.model.dto.school.FinanceSettlementOrgBatchRefundRateDTO;
import com.edu.account.model.query.FinanceSettlementOrganizationQuery;
import com.edu.account.model.vo.FinanceSettlementOrganizationVo;
import com.edu.account.service.FinanceSettlementOrganizationService;
import com.edu.account.utils.FinanceSettlementExcelUtil;
import com.edu.commons.exception.BaseException;
import com.edu.commons.handler.RequestContextHolder;
import com.edu.commons.model.commons.PageInfo;
import com.edu.commons.utils.ConditionAssert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @DESCRIPTION 结算机构管理Service实现
 * @email <EMAIL>
 * @date 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceSettlementOrganizationServiceImpl extends ServiceImpl<FinanceSettlementOrganizationMapper, FinanceSettlementOrganization>
        implements FinanceSettlementOrganizationService {

    private final FinanceSettlementBatchMapper financeSettlementBatchMapper;

    @Override
    public PageInfo<FinanceSettlementOrganizationVo> getPageList(FinanceSettlementOrganizationQuery query) {
        return PageInfo.of(query, () -> baseMapper.selectQuery(query));
    }

    @Override
    public PageInfo<FinanceSettlementOrganizationVo> getPageListVo(FinanceSettlementOrganizationQuery query) {
        return PageInfo.of(query, () -> baseMapper.selectQueryVo(query));
    }

    @Override
    public List<FinanceSettlementOrganizationVo> getBySettlementBatchId(Long settlementBatchId) {
        ConditionAssert.isTrue(settlementBatchId == null, "结算批次ID不能为空");
        return baseMapper.selectBySettlementBatchId(settlementBatchId);
    }

    @Override
    public void createSettlementOrganization(FinanceSettlementOrganization dto) {
        if(dto.getSettlementBatchId() == null){
            throw new BaseException("结算批次ID不能为空");
        }
        if(dto.getSettlementBusinessId() == null || dto.getTwoOrgCode() == null){
            throw new BaseException("结算业务ID或2级组织CODE不能为空");
        }
        baseMapper.insert(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSettlementOrganization(Long id) {
        log.info("删除结算机构，ID：{}", id);

        FinanceSettlementOrganization organization = getById(id);
        ConditionAssert.isTrue(organization == null, "结算机构不存在");
        
        removeById(id);
        
        log.info("结算机构删除成功，ID：{}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteSettlementOrganization(List<Long> ids) {
        log.info("批量删除结算机构，数量：{}", ids.size());
        
        if (CollectionUtils.isNotEmpty(ids)) {
            baseMapper.batchDeleteByIds(ids);
        }
        
        log.info("批量删除结算机构成功，数量：{}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setRefundRate(Long id, BigDecimal refundRate) {
        log.info("设置返费比例，机构ID：{}，返费比例：{}", id, refundRate);

        FinanceSettlementOrganization organization = getById(id);
        ConditionAssert.isTrue(organization == null, "结算机构记录不存在");
        
        // 验证返费比例范围
        ConditionAssert.isTrue(refundRate.compareTo(BigDecimal.ZERO) < 0 || refundRate.compareTo(BigDecimal.ONE) > 0, 
            "返费比例必须在0-1之间");
        
        // 更新返费比例
        FinanceSettlementOrganization updateOrg = FinanceSettlementOrganization.builder()
                .id(id)
                .refundRate(refundRate)
                .build();
        
        updateById(updateOrg);
        
        // 重新计算返费金额
        calculateRefundAmount(id);
        
        log.info("返费比例设置成功，机构ID：{}，返费比例：{}", id, refundRate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSetRefundRate(Long settlementBatchId, FinanceSettlementOrgBatchRefundRateDTO dto) {
        log.info("批量设置返费比例，批次ID：{}，参数：{}", settlementBatchId, dto);
        
        // 验证返费比例范围
        ConditionAssert.isTrue(dto.getRefundRate().compareTo(BigDecimal.ZERO) < 0 || 
            dto.getRefundRate().compareTo(BigDecimal.ONE) > 0, "返费比例必须在0-1之间");
        
        // 批量更新返费比例
        baseMapper.batchSetRefundRate(dto.getIds(), dto.getRefundRate());
        
        // 批量重新计算返费金额
        for (Long orgId : dto.getIds()) {
            calculateRefundAmount(orgId);
        }
        
        log.info("批量设置返费比例成功，批次ID：{}，机构数量：{}", settlementBatchId, dto.getIds().size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateRefundAmount(Long id) {
        log.info("计算返费金额，机构ID：{}", id);

        FinanceSettlementOrganization organization = getById(id);
        if (organization != null && organization.getRefundRate() != null && organization.getTotalPaidAmount() != null) {
            // 返费金额 = 实缴总金额 * 返费比例
            BigDecimal refundAmount = organization.getTotalPaidAmount()
                    .multiply(organization.getRefundRate())
                    .setScale(2, RoundingMode.HALF_UP);
            
            // 更新返费金额
            FinanceSettlementOrganization updateOrg = FinanceSettlementOrganization.builder()
                    .id(id)
                    .refundAmount(refundAmount)
                    .build();

            updateById(updateOrg);
            
            log.info("返费金额计算成功，机构ID：{}，返费金额：{}", id, refundAmount);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditOrganization(Long id, Integer auditStatus, String auditRemark) {
        log.info("审核机构，机构ID：{}，审核状态：{}，审核意见：{}", id, auditStatus, auditRemark);

        FinanceSettlementOrganization organization = getById(id);
        ConditionAssert.isTrue(organization == null, "结算机构不存在");
        
        // 更新审核信息
        FinanceSettlementOrganization updateOrg = FinanceSettlementOrganization.builder()
                .id(id)
                .auditStatus(auditStatus)
                .auditRemark(auditRemark)
                .auditBy(RequestContextHolder.getUserId())
                .auditTime(java.time.LocalDateTime.now())
                .build();
        
        updateById(updateOrg);
        
        log.info("机构审核成功，机构ID：{}，审核状态：{}", id, auditStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAuditOrganization(List<Long> ids, FinanceSettlementOrgAuditStatusEnum auditStatus, String auditRemark) {
        log.info("批量审核机构，机构数量：{}，审核状态：{}", ids.size(), auditStatus);
        
        baseMapper.batchAuditOrganization(ids, auditStatus.getCode(), auditRemark, RequestContextHolder.getUserId());
        
        log.info("批量审核机构成功，机构数量：{}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinanceSettlementOrganizationService.ImportResult importOrganizations(Long settlementBatchId, MultipartFile file) {
        log.info("导入机构，批次ID：{}", settlementBatchId);

        FinanceSettlementOrganizationService.ImportResult result = new FinanceSettlementOrganizationService.ImportResult();
        result.setTotalCount(0);
        result.setSuccessCount(0);
        result.setFailCount(0);

        try {
            // 验证批次是否存在
            FinanceSettlementBatch batch = financeSettlementBatchMapper.selectById(settlementBatchId);
            ConditionAssert.isTrue(batch == null, "财务结算批次不存在");
            ConditionAssert.isTrue(!batch.getBusinessType().isAdvanceEnrollmentFeeSettlement(), "只有预报名费用结算支持机构管理");

            // 使用Excel工具类导入数据
            List<String> errorMessages = FinanceSettlementExcelUtil.importOrganizations(file);
            result.setErrorMessages(errorMessages);

            // TODO: 实现具体的导入逻辑，包括数据保存和统计
            // 这里需要在Excel工具类的监听器中实现具体的保存逻辑

            log.info("机构导入成功，批次ID：{}，成功数量：{}", settlementBatchId, result.getSuccessCount());
        } catch (Exception e) {
            log.error("机构导入失败，批次ID：{}，错误：{}", settlementBatchId, e.getMessage(), e);
            throw new BaseException("机构导入失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public byte[] downloadImportTemplate() {
        log.info("下载机构导入模板");

        try {
            return FinanceSettlementExcelUtil.generateOrganizationImportTemplate();
        } catch (Exception e) {
            log.error("下载机构导入模板失败，错误：{}", e.getMessage(), e);
            throw new BaseException("下载模板失败：" + e.getMessage());
        }
    }

    @Override
    public byte[] exportOrganizations(Long settlementBatchId) {
        log.info("导出机构数据，批次ID：{}", settlementBatchId);

        try {
            List<FinanceSettlementOrganizationVo> organizations = getBySettlementBatchId(settlementBatchId);
            return FinanceSettlementExcelUtil.exportOrganizations(organizations);
        } catch (Exception e) {
            log.error("导出机构数据失败，批次ID：{}，错误：{}", settlementBatchId, e.getMessage(), e);
            throw new BaseException("导出机构数据失败：" + e.getMessage());
        }
    }
}
